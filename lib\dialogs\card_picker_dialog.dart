import 'package:flutter/material.dart';
import '../models.dart';
import 'show_centered_game_area_dialog.dart';

/// Opens a modal dialog with all 52 cards displayed in a grid.
/// Clicking a card immediately confirms & closes.
/// "Remove" button in top-right returns a blank card (rank='', suit='').
void showCardPickerDialog({
  required BuildContext context,
  required void Function(CardModel pickedCard) onCardSelected,
  required List<CardModel> Function() buildDeck,
  String? highlightCardKey,
  required Set<String> usedCards,
}) {
  final allCards = buildDeck();

  showCenteredGameAreaDialog<List<CardModel>?>(
    context: context,
    width: 600,
    height: 450,
    child: StatefulBuilder(
      builder: (dialogCtx, setStateSB) {
        String? hoveredCardKey;

        final showRemoveButton = (highlightCardKey != null);

        return Container(
          color: Colors.blueGrey.shade800,
          child: Column(
            children: [
              // Title bar
              Container(
                height: 40,
                color: Colors.blueGrey.shade700,
                alignment: Alignment.center,
                child: Row(
                  children: [
                    const Text(
                      "Pick a Card",
                      style: TextStyle(color: Colors.white, fontSize: 16),
                    ),
                    const Spacer(),
                    if (showRemoveButton)
                      TextButton(
                        onPressed: () {
                          Navigator.pop(dialogCtx);
                          onCardSelected(CardModel('', '')); 
                        },
                        child: const Text(
                          "Remove",
                          style: TextStyle(color: Colors.red),
                        ),
                      ),
                  ],
                ),
              ),
              // 52-card grid
              Expanded(
                child: GridView.builder(
                  padding: const EdgeInsets.all(8),
                  gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
                    crossAxisCount: 13,
                    childAspectRatio: 0.7,
                  ),
                  itemCount: allCards.length,
                  itemBuilder: (context, index) {
                    final c = allCards[index];
                    final cardKey = '${c.rank}_${c.suit}';
                    final isUsed = usedCards.contains(cardKey);
                    final isHovered = (hoveredCardKey == cardKey);
                    final isHighlight = (highlightCardKey == cardKey);

                    return MouseRegion(
                      cursor: isUsed
                          ? SystemMouseCursors.forbidden
                          : SystemMouseCursors.click,
                      onEnter: (_) {
                        if (!isUsed) {
                          setStateSB(() => hoveredCardKey = cardKey);
                        }
                      },
                      onExit: (_) {
                        if (!isUsed) {
                          setStateSB(() => hoveredCardKey = null);
                        }
                      },
                      child: GestureDetector(
                        onTap: isUsed
                            ? null
                            : () {
                                Navigator.pop(dialogCtx);
                                onCardSelected(c);
                              },
                        child: AnimatedContainer(
                          duration: const Duration(milliseconds: 200),
                          margin: const EdgeInsets.all(4),
                          transform: isHovered
                              ? (Matrix4.identity()..translate(0, -5, 0))
                              : Matrix4.identity(),
                          decoration: BoxDecoration(
                            borderRadius: BorderRadius.circular(6),
                            border: isHighlight
                                ? Border.all(
                                    color: Colors.amberAccent,
                                    width: 3,
                                  )
                                : null,
                            boxShadow: isHovered
                                ? [
                                    BoxShadow(
                                      color: Colors.black26,
                                      offset: const Offset(0, 3),
                                      blurRadius: 5,
                                    )
                                  ]
                                : [],
                          ),
                          child: Opacity(
                            opacity: isUsed ? 0.3 : 1.0,
                            child: _buildCardFront(c, width: 45, height: 65),
                          ),
                        ),
                      ),
                    );
                  },
                ),
              ),
              // bottom action row
              Container(
                color: Colors.blueGrey.shade700,
                padding: const EdgeInsets.symmetric(vertical: 6, horizontal: 16),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.end,
                  children: [
                    TextButton(
                      onPressed: () => Navigator.of(context).pop(),
                      child: const Text("Cancel", style: TextStyle(color: Colors.white70)),
                    ),
                  ],
                ),
              ),
            ],
          ),
        );
      },
    ),
  );
}

Widget _buildCardFront(CardModel c, {double width = 60, double height = 80}) {
  final suitSymbol = (c.suit == "Hearts")
      ? "♥"
      : (c.suit == "Diamonds")
          ? "♦"
          : (c.suit == "Clubs")
              ? "♣"
              : (c.suit == "Spades")
                  ? "♠"
                  : "?";
  final color = (c.suit == "Hearts" || c.suit == "Diamonds")
      ? Colors.red
      : Colors.black;

  return ClipRRect(
    borderRadius: BorderRadius.circular(4),
    child: Container(
      color: Colors.white,
      width: width,
      height: height,
      child: Stack(
        children: [
          Positioned(
            top: 4,
            left: 4,
            child: Text(
              c.rank,
              style: TextStyle(fontSize: 14, fontWeight: FontWeight.bold, color: color),
            ),
          ),
          Positioned(
            top: 22,
            left: 8,
            child: Text(suitSymbol, style: TextStyle(fontSize: 14, color: color)),
          ),
        ],
      ),
    ),
  );
}
