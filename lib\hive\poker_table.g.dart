// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'poker_table.dart';

// **************************************************************************
// TypeAdapterGenerator
// **************************************************************************

class PokerTableAdapter extends TypeAdapter<PokerTable> {
  @override
  final int typeId = 10;

  @override
  PokerTable read(BinaryReader reader) {
    final numOfFields = reader.readByte();
    final fields = <int, dynamic>{
      for (int i = 0; i < numOfFields; i++) reader.readByte(): reader.read(),
    };
    return PokerTable(
      tableId: fields[0] as String,
      isTournament: fields[1] as bool,
      tournamentStartTime: fields[2] as DateTime?,
      prizePool: fields[3] as double?,
      minBuyIn: fields[4] as double,
      maxBuyIn: fields[5] as double,
      seatCount: fields[6] as int,
      smallBlind: fields[7] as int,
      bigBlind: fields[8] as int,
      hasAntes: fields[9] as bool,
      anteAmount: fields[10] as int,
      seatMap: (fields[11] as Map?)?.cast<int, String?>(),
      notes: fields[12] as String,
      totalEntrants: fields[13] as int,
      remainingPlayers: fields[14] as int,
      placesPaid: fields[15] as int,
      payoutStructure: (fields[16] as Map?)?.cast<int, double>(),
      currentLevel: fields[17] as int,
      nextLevelStartTime: fields[18] as DateTime?,
      reentryAllowed: fields[19] as bool,
      maxReentries: fields[20] as int,
      reentryCutoffTime: fields[21] as DateTime?,
      addOnAllowed: fields[22] as bool,
      addOnCutoffTime: fields[23] as DateTime?,
      isBubble: fields[24] as bool,
      bubbleFactor: fields[25] as double,
      useShotClock: fields[26] as bool,
      timeBankSeconds: fields[27] as int,
    );
  }

  @override
  void write(BinaryWriter writer, PokerTable obj) {
    writer
      ..writeByte(28)
      ..writeByte(0)
      ..write(obj.tableId)
      ..writeByte(1)
      ..write(obj.isTournament)
      ..writeByte(2)
      ..write(obj.tournamentStartTime)
      ..writeByte(3)
      ..write(obj.prizePool)
      ..writeByte(4)
      ..write(obj.minBuyIn)
      ..writeByte(5)
      ..write(obj.maxBuyIn)
      ..writeByte(6)
      ..write(obj.seatCount)
      ..writeByte(7)
      ..write(obj.smallBlind)
      ..writeByte(8)
      ..write(obj.bigBlind)
      ..writeByte(9)
      ..write(obj.hasAntes)
      ..writeByte(10)
      ..write(obj.anteAmount)
      ..writeByte(11)
      ..write(obj.seatMap)
      ..writeByte(12)
      ..write(obj.notes)
      ..writeByte(13)
      ..write(obj.totalEntrants)
      ..writeByte(14)
      ..write(obj.remainingPlayers)
      ..writeByte(15)
      ..write(obj.placesPaid)
      ..writeByte(16)
      ..write(obj.payoutStructure)
      ..writeByte(17)
      ..write(obj.currentLevel)
      ..writeByte(18)
      ..write(obj.nextLevelStartTime)
      ..writeByte(19)
      ..write(obj.reentryAllowed)
      ..writeByte(20)
      ..write(obj.maxReentries)
      ..writeByte(21)
      ..write(obj.reentryCutoffTime)
      ..writeByte(22)
      ..write(obj.addOnAllowed)
      ..writeByte(23)
      ..write(obj.addOnCutoffTime)
      ..writeByte(24)
      ..write(obj.isBubble)
      ..writeByte(25)
      ..write(obj.bubbleFactor)
      ..writeByte(26)
      ..write(obj.useShotClock)
      ..writeByte(27)
      ..write(obj.timeBankSeconds);
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is PokerTableAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}
