import 'dart:async';
import 'dart:convert';
import 'package:flutter/material.dart';
import '../models.dart';
import 'override_dialog.dart';
import 'show_centered_game_area_dialog.dart';

/// Opens a single dialog that allows picking multiple cards (numCards).
/// E.g. for the Flop (3), Turn (1), or River (1).
Future<List<CardModel>?> showCombinedStreetPickerDialog({
  required BuildContext context,
  required String streetName,
  required int numCards,
  required void Function(List<CardModel>) onConfirm,
  required List<CardModel> Function() buildDeck,
  required Set<String> usedCards,
  required int? Function(CardModel card) findSeatHoldingCard,
  required CardModel Function(int seatIndex, CardModel oldCard) pickRandomCardForSeat,
}) async {
  // picks[i] is the user’s chosen card
  List<CardModel?> picks = List.filled(numCards, null);
  int? activeIndex = (numCards > 0) ? 0 : null;

  // Build a fresh deck:
  final allCards = buildDeck();
  final chosenCardKeys = <String>{};
  String? randomOverrideKey;

  return showCenteredGameAreaDialog<List<CardModel>?>(
    context: context,
    width: 600,
    height: 450,
    child: StatefulBuilder(
      builder: (dialogCtx, setStateSB) {
        bool allChosen = picks.every((c) => c != null);

        void _maybeAutoAdvance() {
          if (streetName.toLowerCase() == 'flop' && activeIndex != null) {
            if (activeIndex! < numCards - 1) {
              setStateSB(() => activeIndex = activeIndex! + 1);
            }
          }
        }

        void handleGridCardClick(CardModel chosenCard) {
          if (activeIndex == null) return;
          final cardKey = '${chosenCard.rank}_${chosenCard.suit}';

          // Already chosen?
          if (chosenCardKeys.contains(cardKey)) return;

          final isUsed = usedCards.contains(cardKey);
          if (isUsed) {
            showOverrideDialog(
              dialogContext: dialogCtx,
              usedCard: chosenCard,
              onOverride: () {
                // remove from used
                usedCards.remove(cardKey);

                // Possibly remove from whomever had it
                final seatWithCard = findSeatHoldingCard(chosenCard);
                if (seatWithCard != null) {
                  final newRandom = pickRandomCardForSeat(seatWithCard, chosenCard);
                  randomOverrideKey = '${newRandom.rank}_${newRandom.suit}';
                }

                // fill placeholder
                final oldCard = picks[activeIndex!];
                if (oldCard != null) {
                  chosenCardKeys.remove('${oldCard.rank}_${oldCard.suit}');
                }
                picks[activeIndex!] = chosenCard;
                chosenCardKeys.add(cardKey);
                _maybeAutoAdvance();
                setStateSB(() {});
              },
            );
          } else {
            // normal path
            final oldCard = picks[activeIndex!];
            if (oldCard != null) {
              chosenCardKeys.remove('${oldCard.rank}_${oldCard.suit}');
            }
            picks[activeIndex!] = chosenCard;
            chosenCardKeys.add(cardKey);
            _maybeAutoAdvance();
            setStateSB(() {});
          }
        }

        // remove from the currently active placeholder
        void handleRemoveCard() {
          if (activeIndex == null) return;
          final oldCard = picks[activeIndex!];
          if (oldCard != null) {
            chosenCardKeys.remove('${oldCard.rank}_${oldCard.suit}');
            if ('${oldCard.rank}_${oldCard.suit}' == randomOverrideKey) {
              randomOverrideKey = null;
            }
          }
          picks[activeIndex!] = null;
          setStateSB(() {});
        }

        return Container(
          color: Colors.blueGrey.shade800,
          child: Column(
            children: [
              Container(
                height: 40,
                color: Colors.blueGrey.shade700,
                padding: const EdgeInsets.symmetric(horizontal: 16.0),
                child: Row(
                  children: [
                    Text(
                      "Select $streetName",
                      style: const TextStyle(color: Colors.white, fontSize: 16),
                    ),
                    const Spacer(),
                    if (activeIndex != null && picks[activeIndex!] != null)
                      TextButton(
                        onPressed: handleRemoveCard,
                        child: const Text(
                          "Remove",
                          style: TextStyle(color: Colors.red),
                        ),
                      ),
                  ],
                ),
              ),
              // placeholders row
              Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: List.generate(numCards, (i) {
                  final c = picks[i];
                  final isActive = (i == activeIndex);
                  return GestureDetector(
                    onTap: () => setStateSB(() => activeIndex = i),
                    child: Container(
                      width: 50,
                      height: 70,
                      margin: const EdgeInsets.all(4),
                      decoration: BoxDecoration(
                        color: isActive ? Colors.blueGrey.shade300 : Colors.grey.shade300,
                        border: Border.all(
                          color: isActive ? Colors.blue : Colors.black45,
                          width: isActive ? 2 : 1,
                        ),
                      ),
                      child: (c == null)
                          ? const Center(child: Icon(Icons.add))
                          : _buildCardFront(c, width: 50, height: 70),
                    ),
                  );
                }),
              ),
              const SizedBox(height: 4),
              // 52 card grid
              Expanded(
                child: GridView.builder(
                  padding: const EdgeInsets.all(8),
                  gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
                    crossAxisCount: 13,
                    childAspectRatio: 0.7,
                  ),
                  itemCount: allCards.length,
                  itemBuilder: (ctx, index) {
                    final c = allCards[index];
                    final cardKey = '${c.rank}_${c.suit}';
                    final isUsedOrChosen = 
                      usedCards.contains(cardKey) ||
                      chosenCardKeys.contains(cardKey) ||
                      (randomOverrideKey == cardKey);

                    final opacity = isUsedOrChosen ? 0.3 : 1.0;

                    return GestureDetector(
                      onTap: () => handleGridCardClick(c),
                      child: Opacity(
                        opacity: opacity,
                        child: Container(
                          margin: const EdgeInsets.all(2),
                          decoration: BoxDecoration(
                            border: Border.all(color: Colors.black54),
                            borderRadius: BorderRadius.circular(6),
                          ),
                          child: _buildCardFront(c, width: 45, height: 65),
                        ),
                      ),
                    );
                  },
                ),
              ),
              // bottom actions
              Container(
                color: Colors.blueGrey.shade700,
                padding: const EdgeInsets.symmetric(vertical: 6, horizontal: 12),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.end,
                  children: [
                    ElevatedButton(
                      style: ElevatedButton.styleFrom(
                        backgroundColor: allChosen 
                          ? Colors.lightGreenAccent 
                          : Colors.grey.shade600,
                      ),
                      onPressed: allChosen
                          ? () {
                              final chosen = picks.whereType<CardModel>().toList();
                              onConfirm(chosen);
                              Navigator.of(dialogCtx).pop(chosen);
                            }
                          : null,
                      child: const Text("Confirm"),
                    ),
                  ],
                ),
              ),
            ],
          ),
        );
      },
    ),
  );
}

Widget _buildCardFront(CardModel c, {double width = 60, double height = 80}) {
  final suit = c.suit;
  final suitSymbol = (suit == "Hearts")
      ? "♥"
      : (suit == "Diamonds")
          ? "♦"
          : (suit == "Clubs")
              ? "♣"
              : (suit == "Spades")
                  ? "♠"
                  : "?";
  final color = (suit == "Hearts" || suit == "Diamonds") ? Colors.red : Colors.black;

  return ClipRRect(
    borderRadius: BorderRadius.circular(4),
    child: Container(
      width: width,
      height: height,
      color: Colors.white,
      child: Stack(
        children: [
          Positioned(
            top: 4,
            left: 4,
            child: Text(
              c.rank,
              style: TextStyle(fontSize: 14, fontWeight: FontWeight.bold, color: color),
            ),
          ),
          Positioned(
            top: 22,
            left: 8,
            child: Text(suitSymbol, style: TextStyle(fontSize: 14, color: color)),
          ),
        ],
      ),
    ),
  );
}
