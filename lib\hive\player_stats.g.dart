// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'player_stats.dart';

// **************************************************************************
// TypeAdapterGenerator
// **************************************************************************

class PlayerStatsAdapter extends TypeAdapter<PlayerStats> {
  @override
  final int typeId = 2;

  @override
  PlayerStats read(BinaryReader reader) {
    final numOfFields = reader.readByte();
    final fields = <int, dynamic>{
      for (int i = 0; i < numOfFields; i++) reader.readByte(): reader.read(),
    };
    return PlayerStats(
      playerId: fields[0] as String,
      nickname: fields[1] as String,
      lastHandTimestamp: fields[3] as DateTime?,
      handsPlayed: fields[4] as int,
      handsWon: fields[5] as int,
      handsSeenFlop: fields[6] as int,
      handsSeenTurn: fields[7] as int,
      handsSeenRiver: fields[8] as int,
      handsSawShowdown: fields[9] as int,
      totalProfit: fields[10] as double,
      biggestPotWon: fields[11] as double,
      biggestPotLost: fields[12] as double,
      foldedPreflopNoInvestCount: fields[78] as int,
      vpipCount: fields[13] as int,
      pfrCount: fields[14] as int,
      limpCount: fields[15] as int,
      limpFoldCount: fields[16] as int,
      coldCallCount: fields[17] as int,
      threeBetCount: fields[18] as int,
      fourBetCount: fields[19] as int,
      fivePlusBetCount: fields[20] as int,
      foldToThreeBetCount: fields[21] as int,
      foldToFourBetCount: fields[22] as int,
      stealAttemptCount: fields[23] as int,
      foldToStealCount: fields[24] as int,
      threeBetVsStealCount: fields[25] as int,
      avgOpenRaiseSize: fields[26] as double,
      avgThreeBetSize: fields[27] as double,
      avgFourBetSize: fields[28] as double,
      preflopSizingDistribution: (fields[29] as Map?)?.cast<String, int>(),
      openRaiseCount: fields[72] as int,
      foldToOpenCount: fields[73] as int,
      foldToFourBetPlusCount: fields[74] as int,
      openRaiseSamples: fields[75] as int,
      threeBetSamples: fields[76] as int,
      fourBetSamples: fields[77] as int,
      cBetFlopCount: fields[30] as int,
      cBetTurnCount: fields[31] as int,
      cBetRiverCount: fields[32] as int,
      foldToFlopCBetCount: fields[33] as int,
      foldToTurnCBetCount: fields[34] as int,
      foldToRiverCBetCount: fields[35] as int,
      raiseFlopCount: fields[36] as int,
      raiseTurnCount: fields[37] as int,
      raiseRiverCount: fields[38] as int,
      checkRaiseFlopCount: fields[39] as int,
      checkRaiseTurnCount: fields[40] as int,
      checkRaiseRiverCount: fields[41] as int,
      postFlopAggActions: fields[42] as int,
      postFlopAggOpps: fields[43] as int,
      avgFlopBetSize: fields[44] as double,
      avgTurnBetSize: fields[45] as double,
      avgRiverBetSize: fields[46] as double,
      betSizeDistributionFlop: (fields[47] as Map?)?.cast<String, int>(),
      betSizeDistributionTurn: (fields[48] as Map?)?.cast<String, int>(),
      betSizeDistributionRiver: (fields[49] as Map?)?.cast<String, int>(),
      doubleBarrelCount: fields[50] as int,
      tripleBarrelCount: fields[51] as int,
      foldToDoubleBarrelCount: fields[52] as int,
      foldToTripleBarrelCount: fields[53] as int,
      overbetCount: fields[54] as int,
      smallBetCount: fields[55] as int,
      statsByPosition: (fields[56] as Map?)?.map((dynamic k, dynamic v) =>
          MapEntry(k as String, (v as Map).cast<String, int>())),
      potsPlayedHeadsUp: fields[57] as int,
      potsPlayedMultiway: fields[58] as int,
      foldToFlopCBetIP: fields[59] as int,
      foldToFlopCBetOOP: fields[60] as int,
      wentToShowdown: fields[61] as int,
      wonAtShowdown: fields[62] as int,
      wonWithoutShowdown: fields[63] as int,
      bluffCatchCount: fields[64] as int,
      allInEV: fields[65] as double,
      actualWinningsWhenAllIn: fields[66] as double,
      avgTimeToActPreflop: fields[67] as double,
      avgTimeToActPostflop: fields[68] as double,
      totalSessions: fields[69] as int,
      sessionProfit: fields[70] as double,
      notes: fields[71] as String,
    );
  }

  @override
  void write(BinaryWriter writer, PlayerStats obj) {
    writer
      ..writeByte(78)
      ..writeByte(0)
      ..write(obj.playerId)
      ..writeByte(1)
      ..write(obj.nickname)
      ..writeByte(3)
      ..write(obj.lastHandTimestamp)
      ..writeByte(4)
      ..write(obj.handsPlayed)
      ..writeByte(5)
      ..write(obj.handsWon)
      ..writeByte(6)
      ..write(obj.handsSeenFlop)
      ..writeByte(7)
      ..write(obj.handsSeenTurn)
      ..writeByte(8)
      ..write(obj.handsSeenRiver)
      ..writeByte(9)
      ..write(obj.handsSawShowdown)
      ..writeByte(10)
      ..write(obj.totalProfit)
      ..writeByte(11)
      ..write(obj.biggestPotWon)
      ..writeByte(12)
      ..write(obj.biggestPotLost)
      ..writeByte(13)
      ..write(obj.vpipCount)
      ..writeByte(14)
      ..write(obj.pfrCount)
      ..writeByte(15)
      ..write(obj.limpCount)
      ..writeByte(16)
      ..write(obj.limpFoldCount)
      ..writeByte(17)
      ..write(obj.coldCallCount)
      ..writeByte(18)
      ..write(obj.threeBetCount)
      ..writeByte(19)
      ..write(obj.fourBetCount)
      ..writeByte(20)
      ..write(obj.fivePlusBetCount)
      ..writeByte(21)
      ..write(obj.foldToThreeBetCount)
      ..writeByte(22)
      ..write(obj.foldToFourBetCount)
      ..writeByte(23)
      ..write(obj.stealAttemptCount)
      ..writeByte(24)
      ..write(obj.foldToStealCount)
      ..writeByte(25)
      ..write(obj.threeBetVsStealCount)
      ..writeByte(26)
      ..write(obj.avgOpenRaiseSize)
      ..writeByte(27)
      ..write(obj.avgThreeBetSize)
      ..writeByte(28)
      ..write(obj.avgFourBetSize)
      ..writeByte(29)
      ..write(obj.preflopSizingDistribution)
      ..writeByte(72)
      ..write(obj.openRaiseCount)
      ..writeByte(73)
      ..write(obj.foldToOpenCount)
      ..writeByte(74)
      ..write(obj.foldToFourBetPlusCount)
      ..writeByte(75)
      ..write(obj.openRaiseSamples)
      ..writeByte(76)
      ..write(obj.threeBetSamples)
      ..writeByte(77)
      ..write(obj.fourBetSamples)
      ..writeByte(78)
      ..write(obj.foldedPreflopNoInvestCount)
      ..writeByte(30)
      ..write(obj.cBetFlopCount)
      ..writeByte(31)
      ..write(obj.cBetTurnCount)
      ..writeByte(32)
      ..write(obj.cBetRiverCount)
      ..writeByte(33)
      ..write(obj.foldToFlopCBetCount)
      ..writeByte(34)
      ..write(obj.foldToTurnCBetCount)
      ..writeByte(35)
      ..write(obj.foldToRiverCBetCount)
      ..writeByte(36)
      ..write(obj.raiseFlopCount)
      ..writeByte(37)
      ..write(obj.raiseTurnCount)
      ..writeByte(38)
      ..write(obj.raiseRiverCount)
      ..writeByte(39)
      ..write(obj.checkRaiseFlopCount)
      ..writeByte(40)
      ..write(obj.checkRaiseTurnCount)
      ..writeByte(41)
      ..write(obj.checkRaiseRiverCount)
      ..writeByte(42)
      ..write(obj.postFlopAggActions)
      ..writeByte(43)
      ..write(obj.postFlopAggOpps)
      ..writeByte(44)
      ..write(obj.avgFlopBetSize)
      ..writeByte(45)
      ..write(obj.avgTurnBetSize)
      ..writeByte(46)
      ..write(obj.avgRiverBetSize)
      ..writeByte(47)
      ..write(obj.betSizeDistributionFlop)
      ..writeByte(48)
      ..write(obj.betSizeDistributionTurn)
      ..writeByte(49)
      ..write(obj.betSizeDistributionRiver)
      ..writeByte(50)
      ..write(obj.doubleBarrelCount)
      ..writeByte(51)
      ..write(obj.tripleBarrelCount)
      ..writeByte(52)
      ..write(obj.foldToDoubleBarrelCount)
      ..writeByte(53)
      ..write(obj.foldToTripleBarrelCount)
      ..writeByte(54)
      ..write(obj.overbetCount)
      ..writeByte(55)
      ..write(obj.smallBetCount)
      ..writeByte(56)
      ..write(obj.statsByPosition)
      ..writeByte(57)
      ..write(obj.potsPlayedHeadsUp)
      ..writeByte(58)
      ..write(obj.potsPlayedMultiway)
      ..writeByte(59)
      ..write(obj.foldToFlopCBetIP)
      ..writeByte(60)
      ..write(obj.foldToFlopCBetOOP)
      ..writeByte(61)
      ..write(obj.wentToShowdown)
      ..writeByte(62)
      ..write(obj.wonAtShowdown)
      ..writeByte(63)
      ..write(obj.wonWithoutShowdown)
      ..writeByte(64)
      ..write(obj.bluffCatchCount)
      ..writeByte(65)
      ..write(obj.allInEV)
      ..writeByte(66)
      ..write(obj.actualWinningsWhenAllIn)
      ..writeByte(67)
      ..write(obj.avgTimeToActPreflop)
      ..writeByte(68)
      ..write(obj.avgTimeToActPostflop)
      ..writeByte(69)
      ..write(obj.totalSessions)
      ..writeByte(70)
      ..write(obj.sessionProfit)
      ..writeByte(71)
      ..write(obj.notes);
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is PlayerStatsAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}
