import 'package:flutter/material.dart';

class BodyComponent extends StatelessWidget {
  final List<ActionLogItem> actions;

  BodyComponent({
    Key? key,
    this.actions = const  [

    ],
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: BoxDecoration(
        color: Color(0xFF000000).withOpacity(0.48),
        borderRadius: BorderRadius.circular(24),
        border: Border.all(color: Color(0xFF000000).withOpacity(0.32), width: 1),
      ),
      padding: EdgeInsets.all(24),
      child: ListView.builder(
        shrinkWrap: true,
        itemCount: actions.length,
        itemBuilder: (context, index) {
          return Padding(
            padding: EdgeInsets.only(bottom: 16),
            child: ActionLogRow(
              name: actions[index].name,
              action: actions[index].action,
            ),
          );
        },
      ),
    );
  }
}

class ActionLogRow extends StatelessWidget {
  final String name;
  final String action;

  const ActionLogRow({
    Key? key,
    required this.name,
    required this.action,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          name,
          style: TextStyle(
            fontFamily: 'Inter',
            fontWeight: FontWeight.w800,
            fontSize: 14,
            color: Colors.white,
          ),
        ),
        SizedBox(height: 4),
        Text(
          action,
          style: TextStyle(
            fontFamily: 'Inter',
            fontWeight: FontWeight.w500,
            fontSize: 14,
            color: Colors.white.withOpacity(0.64),
          ),
        ),
      ],
    );
  }
}

class ActionLogItem {
  final String name;
  final String action;

  ActionLogItem({required this.name, required this.action});
}

