// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'hand_record.dart';

// **************************************************************************
// TypeAdapterGenerator
// **************************************************************************

class HandRecordAdapter extends TypeAdapter<HandRecord> {
  @override
  final int typeId = 0;

  @override
  HandRecord read(BinaryReader reader) {
    final numOfFields = reader.readByte();
    final fields = <int, dynamic>{
      for (int i = 0; i < numOfFields; i++) reader.readByte(): reader.read(),
    };
    return HandRecord(
      handId: fields[0] as String,
      timestamp: fields[1] as DateTime,
      tableId: fields[2] as String,
      maxPlayers: fields[3] as int,
      playerNames: (fields[4] as List).cast<String>(),
      seatActive: (fields[5] as List).cast<bool>(),
      startingStacks: (fields[6] as List).cast<int>(),
      finalStacks: (fields[7] as List).cast<String>(),
      holeCards: (fields[8] as List)
          .map((dynamic e) => (e as List).cast<String>())
          .toList(),
      dealerIndex: fields[9] as int,
      smallBlindIndex: fields[10] as int,
      bigBlindIndex: fields[11] as int,
      smallBlind: fields[12] as int,
      bigBlind: fields[13] as int,
      burnedCards: (fields[14] as List).cast<String>(),
      boardCards: (fields[15] as List).cast<String>(),
      actionHistory: (fields[16] as List).cast<String>(),
      streetActions: (fields[17] as List).cast<StreetAction>(),
      mainPot: fields[18] as int,
      sidePots: (fields[19] as List)
          .map((dynamic e) => (e as Map).cast<String, dynamic>())
          .toList(),
      winnerSeats: (fields[20] as List).cast<int>(),
      finalHandDescriptions: (fields[21] as Map).cast<int, String>(),
      showdownCards: (fields[22] as Map).map((dynamic k, dynamic v) =>
          MapEntry(k as int, (v as List).cast<String>())),
      seatsSawFlop: (fields[23] as List).cast<int>(),
      seatsSawTurn: (fields[24] as List).cast<int>(),
      seatsSawRiver: (fields[25] as List).cast<int>(),
      preflopAggressorSeat: fields[26] as int?,
      solverData: (fields[27] as Map).cast<String, dynamic>(),
      statsChanges: (fields[28] as Map).map((dynamic k, dynamic v) =>
          MapEntry(k as int, (v as List).cast<String>())),
    );
  }

  @override
  void write(BinaryWriter writer, HandRecord obj) {
    writer
      ..writeByte(29)
      ..writeByte(0)
      ..write(obj.handId)
      ..writeByte(1)
      ..write(obj.timestamp)
      ..writeByte(2)
      ..write(obj.tableId)
      ..writeByte(3)
      ..write(obj.maxPlayers)
      ..writeByte(4)
      ..write(obj.playerNames)
      ..writeByte(5)
      ..write(obj.seatActive)
      ..writeByte(6)
      ..write(obj.startingStacks)
      ..writeByte(7)
      ..write(obj.finalStacks)
      ..writeByte(8)
      ..write(obj.holeCards)
      ..writeByte(9)
      ..write(obj.dealerIndex)
      ..writeByte(10)
      ..write(obj.smallBlindIndex)
      ..writeByte(11)
      ..write(obj.bigBlindIndex)
      ..writeByte(12)
      ..write(obj.smallBlind)
      ..writeByte(13)
      ..write(obj.bigBlind)
      ..writeByte(14)
      ..write(obj.burnedCards)
      ..writeByte(15)
      ..write(obj.boardCards)
      ..writeByte(16)
      ..write(obj.actionHistory)
      ..writeByte(17)
      ..write(obj.streetActions)
      ..writeByte(18)
      ..write(obj.mainPot)
      ..writeByte(19)
      ..write(obj.sidePots)
      ..writeByte(20)
      ..write(obj.winnerSeats)
      ..writeByte(21)
      ..write(obj.finalHandDescriptions)
      ..writeByte(22)
      ..write(obj.showdownCards)
      ..writeByte(23)
      ..write(obj.seatsSawFlop)
      ..writeByte(24)
      ..write(obj.seatsSawTurn)
      ..writeByte(25)
      ..write(obj.seatsSawRiver)
      ..writeByte(26)
      ..write(obj.preflopAggressorSeat)
      ..writeByte(27)
      ..write(obj.solverData)
      ..writeByte(28)
      ..write(obj.statsChanges);
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is HandRecordAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}
