// ai_brain.dart

import 'dart:math' as math;

enum AiDecision {
  fold,
  check,
  call,
  bet,
  raise,
}

class AiBrain {

  /// When [neededToCall] == 0 => there's no bet to call;
  ///   - 10% chance to "bet"
  ///   - else "check"
  ///
  /// When [neededToCall] > 0 => there's a bet out there
  ///   - 10% chance to "raise"
  ///   - 30% chance "fold"
  ///   - remainder "call"
  ///
  /// If [seatIsBigBlindPreflop] is true and hasn't acted yet, we typically want
  /// to check or call if there's no raise above BB. But for simplicity, we do the same logic:
  static AiDecision decideAiAction({
    required int neededToCall,
    required int stack,
    required bool isPreflop,
    required bool seatIsBigBlindPreflop,
  }) {
    final rng = math.Random();
    // If no bet is required:
    if (neededToCall <= 0) {
      // 1% chance to Bet
      if (rng.nextDouble() < 0.01 && stack > 0) {
        return AiDecision.bet;
      } else {
        return AiDecision.check;
      }
    } 
    else {
      // There's a bet. We do ~10% raise, ~30% fold, else call
      final roll = rng.nextDouble();
      if (roll < 0.60) {
        return AiDecision.fold;
      } else if (roll < 0.40) {
        return AiDecision.raise;
      } else {
        return AiDecision.call;
      }
    }
  }
}
