import 'package:flutter/material.dart';

class WinningsDisplay extends StatelessWidget {
  final double amount;
  final String label;

  const WinningsDisplay({
    Key? key,
    this.amount = 17500,
    this.label = 'Winnings',
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 18, vertical: 8),
      margin: EdgeInsets.only(bottom: 30, right: 30),
      decoration: BoxDecoration(
        color: const Color(0x7A000000),
        borderRadius: BorderRadius.circular(100),
        border: Border.all(
          color: const Color(0x14FFFFFF),
          width: 2.5,
        ),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Text(
            label,
            style: const TextStyle(
              fontFamily: 'Inter',
              fontSize: 17,
              fontWeight: FontWeight.w900,
              fontStyle: FontStyle.italic,
              letterSpacing: -0.17,
              color: Color(0xA3FFFFFF), // opacity 0.64
              shadows: [
                Shadow(
                  color: Color(0xE0000000),
                  blurRadius: 2.5,
                ),
              ],
            ),
          ),
          const SizedBox(width: 8),
          Text(
            amount.toStringAsFixed(2),
            style: const TextStyle(
              fontFamily: 'Inter',
              fontSize: 32,
              fontWeight: FontWeight.w900,
              letterSpacing: -0.32,
              color: Colors.white,
              shadows: [
                Shadow(
                  color: Color(0xE0000000),
                  blurRadius: 2.5,
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}

