import 'dart:async';
import 'dart:convert';
import 'package:flutter/material.dart';
import 'package:socket_io_client/socket_io_client.dart' as IO;
import 'package:universal_html/html.dart' as html;

// You'll need to import these from your project
import '../models.dart';
import '../action_history.dart';
import '../poker_table_page.dart';

class SocketListeners {
  final IO.Socket socket;
  final ValueChanged<void> setState;
  final BuildContext context;
  
  // Table state variables - reference to your class variables
  final Map<String, dynamic> tableState;
  final Function(Map<String, dynamic>?) pendingTurnStartData;
  final Function(Map<String, dynamic>) startTurnTimer;
  final Function(String) runStreetFlipAnimation;
  final Function showKnockedOutModal;
  final Function(String, bool) showGameOverModal;
  final Function runCalculateAllThenContinue;
  final Function buildCurrentPotChopItems;
  final Function applyCurrentPotChips;
  final Function finalizePayouts;
  final Function updateBasicStats;
  final Function saveHandRecordToHive;
  final Function resetActionsForNewStreet;
  final Function onSingleDealComplete;
  final Function onSinglePayoutAnimationComplete;
  
  SocketListeners({
    required this.socket,
    required this.setState,
    required this.context,
    required this.tableState,
    required this.pendingTurnStartData,
    required this.startTurnTimer,
    required this.runStreetFlipAnimation,
    required this.showKnockedOutModal,
    required this.showGameOverModal,
    required this.runCalculateAllThenContinue,
    required this.buildCurrentPotChopItems,
    required this.applyCurrentPotChips,
    required this.finalizePayouts,
    required this.updateBasicStats,
    required this.saveHandRecordToHive,
    required this.resetActionsForNewStreet,
    required this.onSingleDealComplete,
    required this.onSinglePayoutAnimationComplete,
  });

  void setupListeners() {
    socket.on("playerListUpdated", (data) {
      if (data is List) {
        setState(() {
          // Optionally reset first, so we rebuild from scratch:
          for (int i = 0; i < tableState['players'].length; i++) {
            if (tableState['players'][i] == null){
              return;
            };
            tableState['players'][i].name = '';
            tableState['players'][i].chips = '0';
            tableState['seatActive'][i]    = false;
          }

          print(tableState['players']);
          print(data);

          for (final p in data) {
            final seatIndex = p['seatNumber'] as int;
            if (seatIndex >= 0 && seatIndex < tableState['players'].length) {
              tableState['players'][seatIndex].id    = p['id']       as String;
              tableState['players'][seatIndex].name  = p['name']  ?? '';
              tableState['players'][seatIndex].chips = p['stack'] ?? '0';
              tableState['seatActive'][seatIndex]    = true;
            }
          }
        });
      }
    });

    socket.on("removedFromTable", (_) {
      showDialog(
        context: context,
        barrierDismissible: false,
        builder: (ctx) {
          return Dialog(
            backgroundColor: const Color(0xFF2E2D2D),
            shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(8)),
            child: Container(
              width: 340,  // same small width
              padding: const EdgeInsets.all(20),
              decoration: BoxDecoration(
                color: const Color(0xFF2E2D2D),
                borderRadius: BorderRadius.circular(8),
                boxShadow: const [
                  BoxShadow(color: Colors.black26, offset: Offset(0,4), blurRadius: 10),
                ],
              ),
              child: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  const Text(
                    "You have been removed from the table.",
                    style: TextStyle(
                      fontSize: 18, fontWeight: FontWeight.bold,
                      color: Color.fromARGB(255, 4, 159, 206),
                    ),
                    textAlign: TextAlign.center,
                  ),
                  const SizedBox(height: 20),
                  ElevatedButton(
                    onPressed: () {
                      Navigator.of(ctx).pop();
                      Navigator.of(context).pushReplacementNamed("/");
                    },
                    style: ElevatedButton.styleFrom(
                      backgroundColor: const Color.fromARGB(255, 4, 159, 206),
                      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(6)),
                    ),
                    child: const Text("OK", style: TextStyle(color: Colors.white)),
                  ),
                ],
              ),
            ),
          );
        },
      );
    });

    socket.on("duplicateSession", (_) {
      showDialog(
        context: context,
        barrierDismissible: false,
        builder: (ctx) {
          return Dialog(
            backgroundColor: const Color(0xFF2E2D2D),
            shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(8)),
            child: Container(
              width: 340,
              padding: const EdgeInsets.all(20),
              decoration: BoxDecoration(
                color: const Color(0xFF2E2D2D),
                borderRadius: BorderRadius.circular(8),
                boxShadow: const [
                  BoxShadow(color: Colors.black26, offset: Offset(0, 4), blurRadius: 10),
                ],
              ),
              child: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  const Text(
                    "Session Replaced",
                    style: TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                      color: Color.fromARGB(255, 4, 159, 206),
                    ),
                    textAlign: TextAlign.center,
                  ),
                  const SizedBox(height: 16),
                  const Text(
                    "You have joined the table on a new location.",
                    style: TextStyle(color: Colors.white),
                    textAlign: TextAlign.center,
                  ),
                  const SizedBox(height: 24),
                  ElevatedButton(
                    onPressed: () {
                      Navigator.of(ctx).pop();
                      Navigator.of(context).pushReplacementNamed("/");
                    },
                    style: ElevatedButton.styleFrom(
                      backgroundColor: const Color.fromARGB(255, 4, 159, 206),
                      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(6)),
                    ),
                    child: const Text("OK", style: TextStyle(color: Colors.white)),
                  ),
                ],
              ),
            ),
          );
        },
      );
    });

    socket.on("turnStart", (data) {
      // always save it
      pendingTurnStartData(data as Map<String, dynamic>);

      if (!tableState['_isDealing']) {
        startTurnTimer(data);
        pendingTurnStartData(null);
      }
    });

    socket.on("knockedOut", (_) {
      showKnockedOutModal();
    });
    
    socket.on("gameOver", (data) {
      print('Received gameOver event');
      
      // First, get my player ID from localStorage
      final myPlayerId = html.window.localStorage['playerId'];
      final winnerId = data["winnerId"] as String?;
      
      // If we've already shown it, bail out
      if (tableState['_hasShownGameOverModal']) {
        print('Already shown game over modal, ignoring');
        return;
      }
      
      // Eliminated players don't get the game over modal
      if (tableState['_isEliminated']) {
        print('Player is eliminated, not showing game over');
        return;
      }
      
      // Stop all ongoing animations and timers
      tableState['_turnTimerController'].stop();
      tableState['_chipCollectController'].stop();
      tableState['_payoutController'].stop();
      tableState['_dealController'].stop();
      
      setState(() {
        tableState['_userActionLock'] = true;
        
        // Set this flag BEFORE showing the modal to prevent race conditions
        tableState['_hasShownGameOverModal'] = true;
      });

      // Only show winner-specific message to the winner
      final bool isWinner = (myPlayerId != null && winnerId != null && myPlayerId == winnerId);
      final winnerServerSeat = data["winnerSeat"] as int?;
      
      // Get winner name
      String winnerName = "???";
      if (winnerServerSeat != null &&
          winnerServerSeat >= 0 &&
          winnerServerSeat < tableState['players'].length) {
        winnerName = tableState['players'][winnerServerSeat].name;
      }
      
      // Show the appropriate modal
      showGameOverModal(winnerName, isWinner);
    });

    socket.on("inactivityBoot", (_) {
      showDialog(
        context: context,
        barrierDismissible: false,
        builder: (_) => AlertDialog(
          title: Text("Removed for Inactivity"),
          content: Text("You have been removed from the table for inactivity."),
          actions: [
            TextButton(
              onPressed: () {
                Navigator.of(context).pushReplacementNamed("/");
              },
              child: Text("OK"),
            ),
          ],
        ),
      );
    });

    socket.on("nextHandCountdown", (data) {
      final secs = data["seconds"] ?? 5;
      setState(() {
        tableState['_showCountdown'] = true;
        if (secs > 0) {
          tableState['_countdownText'] = "$secs..";
        } else {
          // If seconds == 0, show "Next Hand!"
          tableState['_countdownText'] = "Next Hand!";         
        }
      });
    });

    socket.on("showdownResults", (data) {
      print(">>> showdownResults: $data");

      setState(() {
        tableState['gameStage'] = GameStage.showdown;

        final stillIn = tableState['players'].where((p) => !p.isFolded).length;
        if (stillIn > 1) {
          tableState['showAllCards'] = true;
        } else {
          // If only one seat remains, do NOT set showAllCards=true
          tableState['showAllCards'] = false;
        }

        // Clear old pot data
        tableState['_sidePots'].clear();
        tableState['_builtPotChopShares'].clear();

        // We'll track totalPot for display
        int totalPot = 0;

        final potRes = data["potResults"] as List?;
        if (potRes != null) {
          for (final pr in potRes) {
            final potIndex  = pr["potIndex"]  as int;
            final potAmount = pr["potAmount"] as int;
            final winners   = (pr["winnerSeats"] as List).cast<int>();
            final chopSize  = (pr["chopSize"] as num).toDouble();

            // Make sure our _sidePots list is big enough
            while (tableState['_sidePots'].length <= potIndex) {
              tableState['_sidePots'].add(SidePot(0, []));
            }
            tableState['_sidePots'][potIndex] = SidePot(potAmount, winners);
            totalPot += potAmount;

            // Build pot chop info
            tableState['_builtPotChopShares'][potIndex] ??= [];
            if (winners.length == 1) {
              // single winner
              tableState['_builtPotChopShares'][potIndex]!.add({
                "seat": winners.first,
                "amount": potAmount.toDouble(),
              });
            } else {
              // multi-winner
              for (final w in winners) {
                tableState['_builtPotChopShares'][potIndex]!.add({
                  "seat": w,
                  "amount": chopSize,
                });
              }
            }
          }
        }

        tableState['_displayedPot'] = totalPot;
        tableState['_actualPot'] = totalPot;

        // === NEW: CLEAR old winner-loser labels so we can re-set them
        for (int i = 0; i < tableState['players'].length; i++) {
          // If seat was folded or never active, keep them as "Fold" or "..."
          // We'll overwrite only if they ended up in showdown.
          if (tableState['seatActive'][i] && !tableState['players'][i].isFolded) {
            // Temporarily set them to "Lost"
            // We can overwrite with "Win" or "Chop" if we find them below.
            tableState['_playerActions'][i] = "Lost";
          }
        }

        // Next, build seatPotWins or seatPotChops from potRes
        final seatPotWins  = <int,List<int>>{};
        final seatPotChops = <int,List<int>>{};
        for (int i = 0; i < tableState['players'].length; i++) {
          seatPotWins[i]  = [];
          seatPotChops[i] = [];
        }
        // For each pot i => read potResults[i] => fill seatPotWins or seatPotChops
        if (potRes != null) {
          for (final pr in potRes) {
            final potIndex  = pr["potIndex"]  as int;
            final potAmount = pr["potAmount"] as int;
            final winners   = (pr["winnerSeats"] as List).cast<int>();

            if (winners.length == 1) {
              final w = winners.first;
              seatPotWins[w]!.add(potIndex + 1); 
            } else if (winners.length > 1) {
              for (final w in winners) {
                seatPotChops[w]!.add(potIndex + 1);
              }
            }
          }
        }

        // Mark each seat's action label: "Win: Pot 1,2" or "Chop: Pot 1,2" or "Lost"
        tableState['_winnerIndex'] = null; // We'll set this if exactly 1 seat truly "wins" (optional)
        for (int i = 0; i < tableState['players'].length; i++) {
          if (!tableState['seatActive'][i] || tableState['players'][i].isFolded) {
            continue; // folded or not in showdown
          }
          final winsList = seatPotWins[i]!;
          final chopsList= seatPotChops[i]!;

          if (winsList.isEmpty && chopsList.isEmpty) {
            // already set "Lost" above
          } else {
            String winsStr = winsList.isNotEmpty
                ? "Win: Pot ${winsList.join(',')}" : "";
            String chopStr = chopsList.isNotEmpty
                ? "Chop: Pot ${chopsList.join(',')}" : "";

            if (winsStr.isNotEmpty && chopStr.isNotEmpty) {
              tableState['_playerActions'][i] = "$winsStr, $chopStr";
            } else if (winsStr.isNotEmpty) {
              tableState['_playerActions'][i] = winsStr;
              // If you want a single winner highlight, set _winnerIndex if only one pot?
              tableState['_winnerIndex'] = i; 
            } else {
              tableState['_playerActions'][i] = chopStr;
            }
          }
        }

        final newHistory = data["handHistory"] as List<dynamic>?;
        if (newHistory != null && newHistory.isNotEmpty) {
          // Option A: On each new hand, clear the client's old list
                tableState['_handHistory'].clear();
                tableState['_handHistory'].addAll(newHistory.map((e) => e.toString()));

        }    

        // == The usual final step: Start the payout animation
        tableState['gameStage'] = GameStage.payout;
        tableState['_userActionLock'] = true;
        tableState['_payoutAnimatingPotIndex'] = 0;
        tableState['_isAnimatingPayout'] = true;

        if (tableState['_sidePots'].isEmpty) {
          // no side pots => skip straight to finalize
          finalizePayouts();
        } else {
          buildCurrentPotChopItems(0);
          tableState['_payoutController'].forward();
        }
      });
    });

    socket.on("info", (msg) {
      print("[CLIENT] info => $msg");
    });

    socket.on("collectChipsAnimation", (data) async {
      await Future.delayed(const Duration(milliseconds: 500));

      setState(() {
        // 1) Set offsets for each seat:
        for (int i = 0; i < tableState['players'].length; i++) {
          tableState['_chipOffsets'][i] = tableState['_seatBetOffset'](i);
        }

        // 2) Then animate
        tableState['_isCollectingBets'] = true;
        tableState['_chipCollectController'].reset();
        tableState['_chipCollectController'].forward();
      });
    });

    socket.on("joinedMidHand", (data) {
      print(">>> joinedMidHand data => $data");
      final turnIndex    = data["currentTurnIndex"] as int?;
      final deadlineMs   = data["turnDeadline"]    as int? ?? 0;
      final durationMs   = data["turnDurationMs"]  as int? ?? 0;
      final now          = DateTime.now().millisecondsSinceEpoch;
      
      setState(() {
        // 0) reset per-seat arrays
        final seatCount = tableState['players'].length;
        tableState['seatActive']      = List.filled(seatCount, false);
        tableState['_amountsOnTable'] = List.filled(seatCount, 0);
        tableState['_playerActions']  = List.filled(seatCount, "...");
        tableState['_playerControls'] = List.filled(seatCount, false);
        tableState['_pendingBets']    = List.filled(seatCount, 0);

        // 1) extract table state
        final newPot        = data["pot"]              as int?    ?? 0;
        final cBet          = data["currentBet"]       as int?    ?? 0;
        final curStreet     = data["currentStreet"]    as String? ?? "preflop";
        final turnIndex     = data["currentTurnIndex"] as int?;
        final burned        = data["burnedCards"]      as List?   ?? [];
        final community     = data["communityCards"]   as List?   ?? [];
        final foldsArr      = data["isFolded"]         as List?   ?? [];
        final allInArr      = data["isAllIn"]          as List?   ?? [];
        final amountsArr    = data["amountsOnTable"]   as List?   ?? [];
        final seatDataArr   = data["seatData"]         as List?   ?? [];
        final historyArr    = data["handHistory"]      as List?   ?? [];

        // 2) core game state
        tableState['_isGameStarted']    = true;
        tableState['_displayedPot']     = newPot;
        tableState['_actualPot']        = newPot;
        tableState['_currentBet']       = cBet;
        tableState['_currentTurnIndex'] = turnIndex;
        tableState['_serverTurnDurationMs'] = durationMs;

        // 3) set the board / burn based on street
        switch (curStreet) {
          case "flop":
            tableState['gameStage'] = GameStage.flop;
            break;
          case "turn":
            tableState['gameStage'] = GameStage.turn;
            break;
          case "river":
            tableState['gameStage'] = GameStage.river;
            break;
          case "showdown":
            tableState['gameStage'] = GameStage.showdown;
            tableState['showAllCards'] = true;
            break;
          default:
            tableState['gameStage'] = GameStage.preHand;
        }

        // Burn cards
        for (int i = 0; i < 3; i++) {
          if (i < burned.length) {
            final bc = burned[i];
            tableState['burnedCards'][i] = CardModel(bc["rank"], bc["suit"]);
          } else {
            tableState['burnedCards'][i] = CardModel('', '');
          }
        }
        // Community cards
        for (int i = 0; i < 5; i++) {
          if (i < community.length) {
            final cc = community[i];
            tableState['communityCards'][i] = CardModel(cc["rank"], cc["suit"], faceUp: true);
          } else {
            tableState['communityCards'][i] = CardModel('', '', faceUp: false);
          }
        }

        // 4) folded / all-in flags
        for (int i = 0; i < seatCount; i++) {
          tableState['players'][i].isFolded = (i < foldsArr.length && foldsArr[i] == true);
          tableState['players'][i].isAllIn  = (i < allInArr.length  && allInArr[i] == true);
        }

        // 5) how much each has put in this street
        for (final obj in amountsArr) {
          final s = obj["seat"] as int? ?? -1;
          final a = obj["amt"]  as int? ?? 0;
          if (s >= 0 && s < seatCount) {
            tableState['_amountsOnTable'][s] = a;
            tableState['players'][s].contributedThisStreet = a;
          }
        }

        // 6) seat data + position tokens
        //    (Everything in ONE setState so we don't need a second pass)
        tableState['_dealerIndex']      = null;
        tableState['_smallBlindIndex']  = null;
        tableState['_bigBlindIndex']    = null;

        for (final sd in seatDataArr) {
          final s = sd["seatNumber"] as int? ?? -1;
          if (s < 0 || s >= seatCount) continue;

          // basic info
          tableState['players'][s].name  = sd["name"]  as String? ?? "";
          tableState['players'][s].chips = sd["stack"] as String? ?? "0";
          tableState['seatActive'][s]    = !(sd["eliminated"] as bool? ?? false);
          tableState['_playerControls'][s] = (s == tableState['_myServerSeatIndex']);

          // pick up D / SB / BB
          if (sd["dealer"] == true)     tableState['_dealerIndex']     = s;
          if (sd["sb"] == true)         tableState['_smallBlindIndex'] = s;
          if (sd["bb"] == true)         tableState['_bigBlindIndex']   = s;

          // hole cards (only your own will have cards)
          final raw = sd["holeCards"] as List? ?? [];
          if (raw.length >= 2) {
            tableState['players'][s].hand = raw.map((c) {
              return CardModel(c["rank"] as String, c["suit"] as String);
            }).toList();
          } else {
            tableState['players'][s].hand = [];
          }
        }

        // 7) restart your turn timer
        tableState['_turnTimerController']
          ..stop()
          ..reset();

        // 8) replace history
        tableState['_handHistory'] = historyArr.map((e) => e.toString()).toList();

        // 9) lock actions if it's not your turn
        tableState['_userActionLock'] = (tableState['_currentTurnIndex'] != tableState['_myServerSeatIndex']);
      });

      if (turnIndex == tableState['_myServerSeatIndex'] && durationMs > 0) {
        // Compute how much time is left
        final remaining = (deadlineMs - now).clamp(0, durationMs);
        tableState['_turnTimerController']
          ..stop()
          ..duration = Duration(milliseconds: remaining)
          ..reset()
          ..forward();
      }
    });

    socket.on("streetUpdate", (data) {
      // Example structure of data:
      // {
      //   "currentStreet": "flop" | "turn" | "river" | "showdown",
      //   "burnedCards": [...],
      //   "communityCards": [...],
      //   "pot": 600,
      //   "currentBet": 200,
      //   "currentTurnIndex": 2,
      //   "deck": [...],
      //   "isFolded": [...],
      //   "isAllIn": [...],
      //   "amountsOnTable": [...],
      //   "players": [
      //     {"seatNumber": 0, "stack": "1500", "name": "Alice", "dealer": false, "sb": true, ...},
      //     ...
      //   ],
      // }

      //debugPrint(">>> streetUpdate received: $data");

      setState(() {
        // 1) Parse the currentStreet => gameStage
        final streetStr = data["currentStreet"] as String? ?? "preflop";

        final bool isSameStreet = (streetStr == tableState['_lastStreet']);

        switch (streetStr) {
          case "preflop":
            tableState['gameStage'] = GameStage.preHand;
            break;
          case "flop":
            tableState['gameStage'] = GameStage.flop;
            break;
          case "turn":
            tableState['gameStage'] = GameStage.turn;
            break;
          case "river":
            tableState['gameStage'] = GameStage.river;
            break;
          case "showdown":
            tableState['gameStage'] = GameStage.showdown;
            // Optionally set showAllCards = true here if you want
            break;
        }

        // 2) Update pot, currentBet, currentTurnIndex
        tableState['_actualPot']       = data["pot"] ?? 0;
        tableState['_displayedPot']    = tableState['_actualPot'];
        tableState['_currentBet']      = data["currentBet"] ?? 0;
        tableState['_currentTurnIndex']= data["currentTurnIndex"];
        final nextIndex = data["currentTurnIndex"];
        
        if (nextIndex == tableState['_myServerSeatIndex']) {
          tableState['_userActionLock'] = false;
        } else {
          tableState['_userActionLock'] = true;
        }


        // 3) Sync burnedCards + communityCards
        final burnedArr = data["burnedCards"] as List? ?? [];
        for (int i = 0; i < 3; i++) {
          if (i < burnedArr.length) {
            final bc = burnedArr[i];
            tableState['burnedCards'][i] = CardModel(bc["rank"] ?? "", bc["suit"] ?? "");
          } else {
            tableState['burnedCards'][i] = CardModel('', '');
          }
        }
        final commArr = data["communityCards"] as List? ?? [];
        for (int i = 0; i < 5; i++) {
          if (i < commArr.length) {
            final cc = commArr[i];
            final rank = cc["rank"] ?? "";
            final suit = cc["suit"] ?? "";

            // MERGE:
            // Keep our local 'faceUp' if we already set it, else default false
            final wasFaceUp = tableState['communityCards'][i].faceUp;
            tableState['communityCards'][i] = CardModel(rank, suit, faceUp: wasFaceUp);
          } else {
            tableState['communityCards'][i] = CardModel('', '', faceUp: false);
          }
        }

        // 4) Update isFolded, isAllIn, etc.
        final foldArr = data["isFolded"] as List? ?? [];
        final allInArr= data["isAllIn"]   as List? ?? [];
        for (int i = 0; i < tableState['players'].length; i++) {
          tableState['players'][i].isFolded = (i < foldArr.length && foldArr[i] == true);
          tableState['players'][i].isAllIn  = (i < allInArr.length && allInArr[i] == true);
        }

        // _turnTimerController.stop();
        // _turnTimerController
        // ..reset();

        for (int i = 0; i < tableState['_pendingBets'].length; i++) {
          tableState['_pendingBets'][i] = 0;
          tableState['_amountsOnTable'][i] = 0;
          tableState['players'][i].contributedThisStreet = 0;
        }

        // 5) Update amountsOnTable and each player's contributedThisStreet
        for (int i = 0; i < tableState['_amountsOnTable'].length; i++) {
          tableState['_amountsOnTable'][i] = 0;
          tableState['players'][i].contributedThisStreet = 0;
        }
        
        final amtList = data["amountsOnTable"] as List? ?? [];
        for (final obj in amtList) {
          final seat = obj["seat"] as int;
          final amt  = obj["amt"]  as int;
          if (seat >= 0 && seat < tableState['_amountsOnTable'].length) {
            tableState['_amountsOnTable'][seat] = amt;
            tableState['players'][seat].contributedThisStreet = amt;
          }
        }

        // 6) Update each player's stack/name/sb/bb/dealer
        final updatedPlayers = data["players"] as List? ?? [];
        for (final upd in updatedPlayers) {
          final sNum  = upd["seatNumber"] as int;
          if (sNum < 0 || sNum >= tableState['players'].length) continue;

          tableState['players'][sNum].name  = upd["name"]  ?? tableState['players'][sNum].name;
          tableState['players'][sNum].chips = upd["stack"] ?? tableState['players'][sNum].chips;

          // Mark who is dealer/sb/bb if you like:
          // you can also store them as booleans in your Player model if desired
          final isDealer = (upd["dealer"] == true);
          final isSb     = (upd["sb"] == true);
          final isBb     = (upd["bb"] == true);
          if (isDealer)  tableState['_dealerIndex'] = sNum;
          if (isSb)      tableState['_smallBlindIndex'] = sNum;
          if (isBb)      tableState['_bigBlindIndex'] = sNum;
        }

        // 7) Possibly reset your local _playerActions so you can display "..." or similar
        //    or leave them as-is. For example:

        // If we detect that the street *changed* since the last update, do a per-street reset.
        if (streetStr != tableState['_lastStreet']) {
          resetActionsForNewStreet();
          tableState['_lastStreet'] = streetStr;

          runStreetFlipAnimation(streetStr);
        }

        final newHistory = data["handHistory"] as List<dynamic>?;
        if (newHistory != null) {
          // Option A: On each new hand, clear the client's old list
            tableState['_handHistory'].clear();
            tableState['_handHistory'].addAll(newHistory.map((e) => e.toString()));

        }        

        // 8) If gameStage == GameStage.showdown => you might do showAllCards=true
        if (tableState['gameStage'] == GameStage.showdown) {
          final stillIn = tableState['players'].where((p) => !p.isFolded).length;
          if (stillIn > 1) {
            tableState['showAllCards'] = true;
          } else {
            // If only one seat remains, do NOT set showAllCards=true
            tableState['showAllCards'] = false;
          }
          // Optionally trigger some local "showdown" animations
        }

        // Force re-render:
      });
    });

    socket.on("actionUpdate", (data) {
      print(">>> actionUpdate data: $data"); 
      // data is a Map, e.g.:
      // {
      //   "seatIndex": 2,
      //   "action": "raise",
      //   "amount": 500,
      //   "nextTurnIndex": 3,
      //   "pot": 1200,
      //   "currentBet": 500,
      //   "players": [
      //     {"seatNumber":0,"stack":"1000"},
      //     {"seatNumber":1,"stack":"All-in"},
      //     {"seatNumber":2,"stack":"1500"},
      //     {"seatNumber":3,"stack":"800"},
      //     ...
      //   ],
      //   "isFolded":[ false,true,false,false,...],
      //   "isAllIn": [ false,true,false,false,...],
      //   "amountsOnTable":[0,600,500,0, ...]
      // }

      setState(() {
        // 1) Read the simple fields
        final seatIndex = data["seatIndex"] as int;
        final action    = data["action"] as String;
        final amount    = data["amount"] as int;
        final nextIndex = data["nextTurnIndex"] as int?;
        final newPot    = data["pot"] as int;
        final newBet    = data["currentBet"] as int;

        // 2) Update your local 'table' fields
        tableState['_actualPot']       = newPot;
        tableState['_displayedPot']    = newPot;   // or do a fancy chip-collection animation if you want
        tableState['_currentBet']      = newBet;
        tableState['_currentTurnIndex']= nextIndex;

        final isMyTurnNow = tableState['_currentTurnIndex'] == tableState['_myServerSeatIndex'];
        if (isMyTurnNow) {
          tableState['_hasAutoFoldedThisTurn'] = false;
        }        

        // _turnTimerController
        //   ..stop()
        //   ..duration = Duration(milliseconds: _serverTurnDurationMs)
        //   ..reset();

        // if (_currentTurnIndex == _myServerSeatIndex) {
        //   _turnTimerController.forward();
        // }          

        // 3) Update each seat from data["players"]
        final serverPlayers = data["players"];
        if (serverPlayers is List) {
          for (final sp in serverPlayers) {
            final sNum  = sp["seatNumber"] as int;
            final stack = sp["stack"] as String; // e.g. "1000" or "All-in"
            // Now update your local seat's chips:
            tableState['players'][sNum].chips = stack;
          }
        }

        // 4) Sync isFolded[] & isAllIn[] from server
        final foldArr = data["isFolded"] as List<dynamic>;
        final allInArr= data["isAllIn"]   as List<dynamic>;

        for (int i = 0; i < tableState['players'].length; i++) {
          tableState['players'][i].isFolded = (i < foldArr.length && foldArr[i] == true);
          tableState['players'][i].isAllIn  = (i < allInArr.length && allInArr[i] == true);
        }

        final amountsArr = data["amountsOnTable"] as List<dynamic>;
        for (int i = 0; i < tableState['_pendingBets'].length; i++) {
          tableState['_pendingBets'][i] = 0; 
        }

        // First, reset local arrays
        for (int i = 0; i < tableState['_amountsOnTable'].length; i++) {
          tableState['_amountsOnTable'][i] = 0;
          tableState['players'][i].contributedThisStreet = 0;  // <<< add this!
        }

        // Then fill from the server
        for (final obj in amountsArr) {
          final seatIndex = obj["seat"] as int;
          final seatAmt   = obj["amt"]  as int;
          if (seatIndex >= 0 && seatIndex < tableState['_amountsOnTable'].length) {
            tableState['_amountsOnTable'][seatIndex] = seatAmt;
            tableState['_pendingBets'][seatIndex]     = seatAmt;
            tableState['players'][seatIndex].contributedThisStreet = seatAmt; // <<< add this!
          }
        }

        // 6) Optionally set the _playerActions[seatIndex] to something like:
        switch (action) {
          case "fold":
            tableState['_playerActions'][seatIndex] = "Fold";
            break;
          case "check":
            tableState['_playerActions'][seatIndex] = "Check";
            break;
          case "call":
            tableState['_playerActions'][seatIndex] = "Call $amount";
            break;
          case "bet":
            // If they bet their entire stack => "All-in {amt}"
            final stackStr = tableState['players'][seatIndex].chips.toLowerCase();
            if (stackStr == "all-in") {
              tableState['_playerActions'][seatIndex] = "All-in $amount";
            } else {
              tableState['_playerActions'][seatIndex] = "Bet $amount";
            }
            break;
          case "raise":
            // same logic
            final stackStr2 = tableState['players'][seatIndex].chips.toLowerCase();
            if (stackStr2 == "all-in") {
              tableState['_playerActions'][seatIndex] = "All-in $amount";
            } else {
              // or "Raise to (their contributedThisStreet)" if you prefer
              tableState['_playerActions'][seatIndex] = "Raise $amount";
            }
            break;
        }

        final newHistory = data["handHistory"] as List<dynamic>?;
        if (newHistory != null) {
          // Option A: On each new hand, clear the client's old list
            tableState['_handHistory'].clear();
            tableState['_handHistory'].addAll(newHistory.map((e) => e.toString()));

        }

        // 7) If the nextTurnIndex is MY seat => enable my action UI
        //    else disable it (or let the seat logic handle that).
        print(tableState['_currentTurnIndex']);
        print(tableState['_myServerSeatIndex']);
        if (tableState['_currentTurnIndex'] == tableState['_myServerSeatIndex']) {
          tableState['_userActionLock'] = false;
        } else {
          tableState['_userActionLock'] = true;
        }

        // 8) Done
      });
    });

    socket.on("startHand", (data) {
      print(">>> startHand received:");
      // Example data structure:
      // {
      //   "players": [
      //     {
      //       "seatNumber": 0,
      //       "name": "Alice",
      //       "stack": "2000",
      //       "cards": [
      //         {"rank": "K", "suit": "Hearts"},
      //         {"rank": "6", "suit": "Diamonds"}
      //       ],
      //       "dealer": true,
      //       "sb": false,
      //       "bb": true,
      //       "eliminated": false
      //     },
      //     ...
      //   ],
      //   "pot": 0,
      //   "burnedCards": [],
      //   "communityCards": [],
      //   "currentBet": 200,
      //   "currentTurnIndex": 1,
      //   "deck": [...],
      //   "isFolded": [ false, false, ...],
      //   "isAllIn":   [ false, false, ...],
      //   "amountsOnTable": [ 100, 200, ...],
      //   "handHistory": [...],
      //   ...
      // }

      final updatedPlayers = data["players"];
      if (updatedPlayers is List) {
        setState(() {
          // Clear old side-pot data, animations, etc.
          tableState['_sidePots'].clear();
          tableState['_builtPotChopShares'].clear();
          tableState['_payoutAnimatingPotIndex'] = 0;
          tableState['_payoutController'].reset();

          // Reset pot & countdown
          tableState['_displayedPot'] = 0;
          tableState['_showCountdown'] = false;
          tableState['_countdownText'] = "";

          // Mark game as started
          tableState['_isGameStarted'] = true;
          tableState['showAllCards']   = false;
          tableState['gameStage']      = GameStage.preHand;
          tableState['_historyPanelState'] = HistoryPanelState.semiOpen;

          // Clear each player's local 'hand'
          for (int i = 0; i < tableState['players'].length; i++) {
            tableState['players'][i].hand.clear();
          } 

          // Possibly update our local handHistory from server
          final newHistory = data["handHistory"] as List<dynamic>?;
          if (newHistory != null) {
            tableState['_handHistory'].clear();
            tableState['_handHistory'].addAll(newHistory.map((e) => e.toString()));
          }

          // Temporary variables for (server) seat indexes
          int? dealerIndexServer;
          int? smallBlindIndexServer;
          int? bigBlindIndexServer;

          // We'll set these after we parse the players
          tableState['_dealerIndex']      = null;
          tableState['_smallBlindIndex']  = null;
          tableState['_bigBlindIndex']    = null;
          
          tableState['_serverTurnDurationMs'] = data["turnDurationMs"] as int;

          tableState['_hasAutoFoldedThisTurn'] = false;
          tableState['_turnTimerController']
            ..stop()
            ..duration = Duration(milliseconds: data["turnDurationMs"])
            ..reset()
            ..forward();

          // ----------------------
          // Populate seat data
          // ----------------------
          for (final p in updatedPlayers) {
            final seatIndex = p["seatNumber"] as int;

            // Overwrite local seat data
            tableState['players'][seatIndex].name       = p["name"]  ?? "";
            tableState['players'][seatIndex].chips      = p["stack"] ?? "0";
            tableState['players'][seatIndex].eliminated = (p["eliminated"] == true);

            // If eliminated => seat inactive, folded, label "Eliminated"
            if (tableState['players'][seatIndex].eliminated) {
              //seatActive[seatIndex]      = false;
              tableState['players'][seatIndex].isFolded = true;
              tableState['_playerActions'][seatIndex]   = "Eliminated";
            } else {
              tableState['seatActive'][seatIndex] = true;
              // we'll set folded/allIn below from isFolded[], isAllIn[] arrays
            }

            // Check if this seat is dealer / sb / bb
            if (p["dealer"] == true) {
              dealerIndexServer = seatIndex;
            }
            if (p["sb"] == true) {
              smallBlindIndexServer = seatIndex;
            }
            if (p["bb"] == true) {
              bigBlindIndexServer = seatIndex;
            }
          }

          // Convert server seat indexes to local
          if (dealerIndexServer != null) {
            tableState['_dealerIndex'] = dealerIndexServer;
          }
          if (smallBlindIndexServer != null) {
            tableState['_smallBlindIndex'] = smallBlindIndexServer;
          }
          if (bigBlindIndexServer != null) {
            tableState['_bigBlindIndex'] = bigBlindIndexServer;
          }

          // ----------------------
          // Burned & community cards
          // ----------------------
          final dynamicBurned = data["burnedCards"] as List? ?? [];
          tableState['burnedCards'] = List.generate(3, (i) {
            if (i < dynamicBurned.length) {
              final bc = dynamicBurned[i];
              return CardModel(bc["rank"] ?? "", bc["suit"] ?? "");
            }
            return CardModel('', '');
          });

          final dynamicComm = data["communityCards"] as List? ?? [];
          tableState['communityCards'] = List.generate(5, (i) {
            if (i < dynamicComm.length) {
              final cc = dynamicComm[i];
              return CardModel(cc["rank"] ?? "", cc["suit"] ?? "");
            }
            return CardModel('', '');
          });

          // ----------------------
          // Pot, bet, turn index
          // ----------------------
          tableState['_actualPot']       = data["pot"] ?? 0;
          tableState['_displayedPot']    = tableState['_actualPot'];
          tableState['_currentBet']      = data["currentBet"] ?? 0;
          tableState['_currentTurnIndex']= data["currentTurnIndex"];

          // _turnTimerController.stop();   
          // _turnTimerController
          // .reset();

          if (tableState['_currentTurnIndex'] == tableState['_myServerSeatIndex']) {
            tableState['_userActionLock'] = false;
          } else {
            tableState['_userActionLock'] = true;
          }

          // ----------------------
          // isFolded, isAllIn, amountsOnTable
          // ----------------------
          final ifArray = data["isFolded"] as List? ?? [];
          final iaArray = data["isAllIn"]   as List? ?? [];
          final newArr  = data["amountsOnTable"] as List? ?? [];

          for (int i = 0; i < tableState['players'].length; i++) {
            tableState['players'][i].isFolded = (i < ifArray.length && ifArray[i] == true);
            tableState['players'][i].isAllIn  = (i < iaArray.length && iaArray[i] == true);
          }
          for (int i = 0; i < tableState['_amountsOnTable'].length; i++) {
            tableState['_amountsOnTable'][i] = 0;
            tableState['players'][i].contributedThisStreet = 0;
          }
          for (final obj in newArr) {
            final seatNum = obj["seat"] as int;
            final seatAmt = obj["amt"]  as int;
            if (seatNum >= 0 && seatNum < tableState['_amountsOnTable'].length) {
              tableState['_amountsOnTable'][seatNum] = seatAmt;
              tableState['players'][seatNum].contributedThisStreet = seatAmt;
            }
          }

          // leftover deck (if you want animations)
          final leftoverDeck = data["deck"] as List? ?? [];
          tableState['deck'].clear();
          for (final d in leftoverDeck) {
            final rank = d["rank"] ?? "";
            final suit = d["suit"] ?? "";
            tableState['deck'].add(CardModel(rank, suit));
          }

          // Show blinds in the UI
          for (int i = 0; i < tableState['_playerActions'].length; i++) {
            if (tableState['players'][i].eliminated) {
              tableState['_playerActions'][i] = "Eliminated";
            } else {
              tableState['_playerActions'][i] = "...";
            }
          }
          tableState['_handHistory'].add(" ");

          // If server says sb/bb => display "SB ___" or "BB ___"
          for (final p in updatedPlayers) {
            final sIndex = p["seatNumber"] as int;
            final posted = tableState['_amountsOnTable'][sIndex];
            if (p["sb"] == true && posted > 0) {
              tableState['_playerActions'][sIndex] = "SB $posted";
            } else if (p["bb"] == true && posted > 0) {
              tableState['_playerActions'][sIndex] = "BB $posted";
            }
          }

          // ----------------------
          // Build dealing queue
          // ----------------------
          tableState['_cardsToDeal'].clear();

          var foundDealer = updatedPlayers.firstWhere(
            (p) => p["dealer"] == true,
            orElse: () => null
          );

          if (foundDealer == null) {
            print("No dealer found; defaulting to seat 0");
            foundDealer = updatedPlayers.first; 
          }
          int actualDealerIndex = foundDealer["seatNumber"] as int;

          // Then build seatNumbers normally:
          final seatNumbers = updatedPlayers.map<int>((p) => p["seatNumber"] as int).toList();
          final dealerPos = seatNumbers.indexOf(actualDealerIndex);
          if (dealerPos < 0) {
            print("Dealer index not found in seatNumbers.");
            return; 
          }

          // Rebuild dealingOrder, skipping no one yet:
          final dealingOrder = <int>[];
          for (int i = 1; i <= seatNumbers.length; i++) {
            final seatNum = seatNumbers[(dealerPos + i) % seatNumbers.length];
            dealingOrder.add(seatNum);
          }

          // Finally the two-pass deal, skipping eliminated seats:
          tableState['_cardsToDeal'].clear();
          for (int pass = 0; pass < 2; pass++) {
            for (final seatNum in dealingOrder) {
              final seatObj = updatedPlayers.firstWhere(
                (p) => p["seatNumber"] == seatNum,
                orElse: () => null
              );
              if (seatObj == null) continue;

              // Skip if seatObj is eliminated
              if (seatObj["eliminated"] == true) continue;

              final seatCards = seatObj["cards"] as List? ?? [];
              if (pass < seatCards.length) {
                final cardData = seatCards[pass];
                final rank = cardData["rank"] ?? "";
                final suit = cardData["suit"] ?? "";
                final cardModel = CardModel(rank, suit);
                tableState['_cardsToDeal'].add((seatNum, cardModel));
              }
            }
          }

          // At the end:
          tableState['_isDealing'] = true;
          tableState['_dealingIndex'] = 0;
          onSingleDealComplete();

        });
      }
    });
  }
}