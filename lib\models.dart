import 'dart:ui';

import 'package:flutter/material.dart';
import 'package:four_leaf_poker/ai_personality.dart';
import 'package:four_leaf_poker/hive/player_stats.dart';

/// =============================
///      MODEL CLASSES
/// =============================

/// CardModel represents a single playing card.
class CardModel {
  final String rank; // "2","3","4","5","6","7","8","9","10","J","Q","K","A"
  final String suit; // "Clubs","Diamonds","Hearts","Spades"
  bool faceUp;

  CardModel(this.rank, this.suit, {this.faceUp = false});
}

class SidePot {
  final int amount;
  final List<int> eligibles;
  SidePot(this.amount, this.eligibles);
}

class SeatOffset {
  final Offset circle;
  final Offset tab;
  const SeatOffset(this.circle, this.tab);
}

/// Player represents a seat/player at the table.
class Player {
  String id;
  int seatNumber;            // seatIndex from the server
  String name;
  String chips;
  List<CardModel> hand;
  PlayerStats? playerStats;
  int contributedThisStreet;
  int contributedTotal;
  bool eliminated;

  // Add these two booleans:
  bool isFolded;
  bool isAllIn;

  Player({
    required this.id,
    required this.seatNumber,
    required this.name,
    required this.chips,
    List<CardModel>? hand,
    this.contributedThisStreet = 0,
    this.contributedTotal = 0,
    this.isFolded = false,
    this.isAllIn = false,
    this.eliminated = false,
    this.playerStats,
  }) : hand = hand ?? [];
}


enum FastForwardReason {
  none,        // Not fast-forwarding
  allFolded,   // Only one player left with chips
  allAllIn,    // Everyone is all in
}

class ChopPayoutItem {
  final int seatIndex;
  final String label;
  final Offset start;
  final Offset end;

  ChopPayoutItem({
    required this.seatIndex,
    required this.label,
    required this.start,
    required this.end,
  });
}

class PotLabel extends StatefulWidget {
  final int potIndex;
  final String label;
  final bool isSmall;  // add this
  final Function(int potIndex, Offset globalPos) onMeasured;

  const PotLabel({
    Key? key,
    required this.potIndex,
    required this.label,
    required this.onMeasured,
    this.isSmall = false,  // default false
  }) : super(key: key);

  @override
  State<PotLabel> createState() => PotLabelState();
}

class PotLabelState extends State<PotLabel> {
  @override
  void initState() {
    super.initState();
    // We can't measure here because the widget isn't laid out yet.
  }

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    // Delaying the measurement to after layout
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _measureAndReport();
    });
  }

  void _measureAndReport() {
    final renderObj = context.findRenderObject();
    if (renderObj is RenderBox) {
      final offset = renderObj.localToGlobal(Offset.zero);
      widget.onMeasured(widget.potIndex, offset);
    }
  }

  @override
  Widget build(BuildContext context) {
    final double fs = widget.isSmall ? 12.0 : 16.0; // reduce if needed
    return Text(
      widget.label,
      style: TextStyle(
        color: Colors.black,
        fontWeight: FontWeight.bold,
        fontSize: fs,
      ),
    );
  }
}