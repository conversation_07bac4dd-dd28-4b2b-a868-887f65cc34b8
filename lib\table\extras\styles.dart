import 'package:flutter/material.dart';

TextStyle headerTextStyle() {
  return const TextStyle(
    fontFamily: 'Inter',
    fontWeight: FontWeight.w900,
    fontSize: 16,
    color: Colors.white,
    shadows: [
      Shadow(
        color: Color(0xE0000000),
        blurRadius: 2.5,
      ),
    ],
  );
}

TextStyle bodyTextStyle() {
  return const TextStyle(
    fontFamily: 'Inter',
    fontWeight: FontWeight.w500,
    fontSize: 14,
    color: Colors.white,
  );
}

ButtonStyle footerButtonStyle() {
  return ElevatedButton.styleFrom(
    backgroundColor: const Color(0xFF004447),
    shape: RoundedRectangleBorder(
      borderRadius: BorderRadius.circular(16),
      side: const BorderSide(
        color: Color(0xCC000000),
        width: 2.5,
      ),
    ),
    padding: EdgeInsets.zero,
    elevation: 0,
  ).copyWith(
    overlayColor: MaterialStateProperty.resolveWith<Color?>(
      (Set<MaterialState> states) {
        if (states.contains(MaterialState.hovered)) {
          return Colors.white.withOpacity(0.1);
        }
        return null;
      },
    ),
  );
}

TextStyle footerTextStyle() {
  return const TextStyle(
    fontFamily: 'Inter',
    fontWeight: FontWeight.w900,
    fontSize: 18,
    color: Colors.white,
    fontStyle: FontStyle.italic,
    shadows: [
      Shadow(
        color: Color(0xE0000000),
        blurRadius: 0,
        offset: Offset(2.5, 2.5),
      ),
    ],
  );
}

