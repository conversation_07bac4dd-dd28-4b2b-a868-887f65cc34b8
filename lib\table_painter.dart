import 'dart:ui' as ui;
import 'package:flutter/material.dart';

/// Draws the poker-table oval background with debossed (pushed-in) text.
class TablePainter extends CustomPainter {
  final Color tableColor;

  TablePainter({required this.tableColor});

  @override
  void paint(Canvas canvas, Size size) {
    // 1) Draw the oval table background
    final fill = Paint()
      ..color = tableColor
      ..style = PaintingStyle.fill;

    final border = Paint()
      ..color = const Color.fromARGB(255, 63, 63, 63)
      ..style = PaintingStyle.stroke
      ..strokeWidth = 10;

    final rect = Rect.fromLTWH(20, 20, size.width - 40, size.height - 40);

    canvas.drawOval(rect, fill);
    canvas.drawOval(rect, border);

    // 2) Draw "4LeafPoker" debossed near the top of the table
    _drawDebossedText(canvas, size, "4LeafPoker");
  }

  /// Paints text with shadow/highlight offsets reversed to appear 'pushed into' the surface.
  void _drawDebossedText(Canvas canvas, Size size, String text) {
    // Position the text somewhat above center (150 px up)
    final centerX = size.width  / 2;
    final centerY = size.height / 2 - 125;

    const double fontSize = 32.0;
    final baseStyle = TextStyle(
      fontSize: fontSize,
      fontWeight: FontWeight.bold,
    );

    // === 1) Top-left shadow (darker) ===
    // This is the "indented" edge on top-left.
    final textPainterDark = TextPainter(
      text: TextSpan(
        text: text,
        style: baseStyle.copyWith(
          color: Colors.black.withOpacity(0.35),
        ),
      ),
      textAlign: TextAlign.center,
      textDirection: ui.TextDirection.ltr,
    )..layout();

    final darkSize = textPainterDark.size;
    textPainterDark.paint(
      canvas,
      Offset(centerX - darkSize.width / 2 - 2, centerY - darkSize.height / 2 - 2),
    );

    // === 2) Bottom-right highlight (lighter) ===
    // This is the subtle highlight on the bottom-right inner edge.
    final textPainterLight = TextPainter(
      text: TextSpan(
        text: text,
        style: baseStyle.copyWith(
          color: Colors.white.withOpacity(0.3),
        ),
      ),
      textAlign: TextAlign.center,
      textDirection: ui.TextDirection.ltr,
    )..layout();

    final lightSize = textPainterLight.size;
    textPainterLight.paint(
      canvas,
      Offset(centerX - lightSize.width / 2 + 2, centerY - lightSize.height / 2 + 2),
    );

    // === 3) Main text in a color close to tableColor ===
    // By making it slightly darker, it looks 'cut in' or pushed in.
    final textPainterMain = TextPainter(
      text: TextSpan(
        text: text,
        style: baseStyle.copyWith(
          color: tableColor.withOpacity(0.75), 
        ),
      ),
      textAlign: TextAlign.center,
      textDirection: ui.TextDirection.ltr,
    )..layout();

    final mainSize = textPainterMain.size;
    textPainterMain.paint(
      canvas,
      Offset(centerX - mainSize.width / 2, centerY - mainSize.height / 2),
    );
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => false;
}
