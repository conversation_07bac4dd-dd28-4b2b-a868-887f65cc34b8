import 'package:hive/hive.dart';
import 'package:four_leaf_poker/hive/street_action.dart';

part 'hand_record.g.dart'; // Must match your file name

@HiveType(typeId: 0)
class HandRecord extends HiveObject {
  // BASIC IDENTIFIERS
  @HiveField(0)
  final String handId;

  @HiveField(1)
  final DateTime timestamp;

  @HiveField(2)
  final String tableId;

  @HiveField(3)
  final int maxPlayers;

  // PLAYER / SEAT INFO
  @HiveField(4)
  final List<String> playerNames;

  @HiveField(5)
  final List<bool> seatActive;

  @HiveField(6)
  final List<int> startingStacks;

  @HiveField(7)
  final List<String> finalStacks;

  @HiveField(8)
  final List<List<String>> holeCards;

  @HiveField(9)
  final int dealerIndex;
  @HiveField(10)
  final int smallBlindIndex;
  @HiveField(11)
  final int bigBlindIndex;
  @HiveField(12)
  final int smallBlind;
  @HiveField(13)
  final int bigBlind;

  // BOARD / RUNOUT
  @HiveField(14)
  final List<String> burnedCards;
  @HiveField(15)
  final List<String> boardCards;

  // ACTION HISTORY
  @HiveField(16)
  final List<String> actionHistory;
  @HiveField(17)
  final List<StreetAction> streetActions;

  // POTS
  @HiveField(18)
  final int mainPot;
  @HiveField(19)
  final List<Map<String, dynamic>> sidePots;

  // SHOWDOWN
  @HiveField(20)
  final List<int> winnerSeats;
  @HiveField(21)
  final Map<int, String> finalHandDescriptions;
  @HiveField(22)
  final Map<int, List<String>> showdownCards;

  // WHO SAW WHICH STREET
  @HiveField(23)
  final List<int> seatsSawFlop;
  @HiveField(24)
  final List<int> seatsSawTurn;
  @HiveField(25)
  final List<int> seatsSawRiver;

  // EXTRA METADATA
  @HiveField(26)
  final int? preflopAggressorSeat;
  @HiveField(27)
  final Map<String, dynamic> solverData;

  // NEW FIELD: statsChanges - NOT FINAL, NOT REQUIRED
  @HiveField(28)
  Map<int, List<String>> statsChanges;

  HandRecord({
    required this.handId,
    required this.timestamp,
    required this.tableId,
    required this.maxPlayers,
    required this.playerNames,
    required this.seatActive,
    required this.startingStacks,
    required this.finalStacks,
    required this.holeCards,
    required this.dealerIndex,
    required this.smallBlindIndex,
    required this.bigBlindIndex,
    required this.smallBlind,
    required this.bigBlind,
    required this.burnedCards,
    required this.boardCards,
    required this.actionHistory,
    required this.streetActions,
    required this.mainPot,
    required this.sidePots,
    required this.winnerSeats,
    required this.finalHandDescriptions,
    required this.showdownCards,
    required this.seatsSawFlop,
    required this.seatsSawTurn,
    required this.seatsSawRiver,
    this.preflopAggressorSeat,
    required this.solverData,

    // Provide default empty map if you don't supply it in the constructor
    this.statsChanges = const {},
  });
}
