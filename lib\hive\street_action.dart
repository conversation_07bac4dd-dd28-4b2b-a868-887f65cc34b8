import 'package:hive/hive.dart';

part 'street_action.g.dart';

@HiveType(typeId: 1)
class StreetAction {
  @HiveField(0)
  final int seatIndex; // which seat took the action

  @HiveField(1)
  final String action; // e.g. "bet", "raise", "call", "fold", "check"

  @HiveField(2)
  final int amount;    // e.g. 800 or 0 if fold/check

  @HiveField(3)
  final String streetName; // "Preflop", "Flop", "Turn", "River"

  StreetAction({
    required this.seatIndex,
    required this.action,
    required this.amount,
    required this.streetName,
  });
}
