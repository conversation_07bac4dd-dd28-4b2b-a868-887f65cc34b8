// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'street_action.dart';

// **************************************************************************
// TypeAdapterGenerator
// **************************************************************************

class StreetActionAdapter extends TypeAdapter<StreetAction> {
  @override
  final int typeId = 1;

  @override
  StreetAction read(BinaryReader reader) {
    final numOfFields = reader.readByte();
    final fields = <int, dynamic>{
      for (int i = 0; i < numOfFields; i++) reader.readByte(): reader.read(),
    };
    return StreetAction(
      seatIndex: fields[0] as int,
      action: fields[1] as String,
      amount: fields[2] as int,
      streetName: fields[3] as String,
    );
  }

  @override
  void write(BinaryWriter writer, StreetAction obj) {
    writer
      ..writeByte(4)
      ..writeByte(0)
      ..write(obj.seatIndex)
      ..writeByte(1)
      ..write(obj.action)
      ..writeByte(2)
      ..write(obj.amount)
      ..writeByte(3)
      ..write(obj.streetName);
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is StreetActionAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}
