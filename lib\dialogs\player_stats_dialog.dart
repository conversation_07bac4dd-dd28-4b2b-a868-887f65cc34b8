import 'package:flutter/material.dart';
import 'package:four_leaf_poker/hive/player_stats.dart';
import '../models.dart'; // your Player model, etc.

class PlayerStatsDialog extends StatefulWidget {
  final List<Player> players;
  final List<bool> seatActive;

  const PlayerStatsDialog({
    Key? key,
    required this.players,
    required this.seatActive,
  }) : super(key: key);

  @override
  State<PlayerStatsDialog> createState() => _PlayerStatsDialogState();
}

class _PlayerStatsDialogState extends State<PlayerStatsDialog> {
  // We’ll use a ScrollController to make sure the horizontal scrollbar is displayed and functional.
  final ScrollController _horizontalScrollCtrl = ScrollController();

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      backgroundColor: Colors.blueGrey.shade900,
      insetPadding: const EdgeInsets.symmetric(horizontal: 50, vertical: 24),
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      title: Row(
        children: [
          const Expanded(
            child: Text(
              "Player Stats Overview",
              style: TextStyle(color: Colors.white, fontWeight: FontWeight.bold),
            ),
          ),
          IconButton(
            icon: const Icon(Icons.close, color: Colors.white70),
            onPressed: () => Navigator.of(context).pop(),
          ),
        ],
      ),
      content: SizedBox(
        width: MediaQuery.of(context).size.width * 0.8,
        height: MediaQuery.of(context).size.height * 0.7,
        // We wrap our SingleChildScrollView in a Scrollbar for horizontal.
        child: Scrollbar(
          controller: _horizontalScrollCtrl,
          thumbVisibility: true, // Always show the scrollbar thumb
          trackVisibility: true, // Optional: show the scrollbar track
          child: SingleChildScrollView(
            controller: _horizontalScrollCtrl,
            scrollDirection: Axis.horizontal,
            child: _buildDataTable(),
          ),
        ),
      ),
      contentPadding: const EdgeInsets.fromLTRB(24, 16, 24, 24),
    );
  }

  Widget _buildDataTable() {
    // Gather all indices that are active
    final activeIndices = <int>[];
    for (int i = 0; i < widget.seatActive.length; i++) {
      if (widget.seatActive[i]) activeIndices.add(i);
    }

    if (activeIndices.isEmpty) {
      return const Center(
        child: Text(
          "No active players to display.",
          style: TextStyle(color: Colors.white),
        ),
      );
    }

    // Build the columns and rows of the DataTable
    final columns = _buildDataColumns();
    final rows = <DataRow>[];

    for (final i in activeIndices) {
      final player = widget.players[i];
      final stats = player.playerStats;
      rows.add(_buildDataRow(i, player, stats));
    }

    return DataTable(
      headingRowColor: MaterialStateProperty.resolveWith(
        (states) => Colors.blueGrey.shade800,
      ),
      dataRowColor: MaterialStateProperty.resolveWith(
        (states) => Colors.blueGrey.shade700,
      ),
      columns: columns,
      rows: rows,
    );
  }

  /// Helper to build a DataColumn with a short label + "!" tooltip
  DataColumn _colStat(String shortLabel, String definition) {
    return DataColumn(
      label: Row(
        children: [
          Text(
            shortLabel,
            style: const TextStyle(
              color: Colors.lightGreenAccent,
              fontWeight: FontWeight.bold,
              fontSize: 12,
            ),
          ),
          const SizedBox(width: 4),
          // The "!" icon with hover tooltip
          Tooltip(
            message: definition,
            decoration: BoxDecoration(
              color: Colors.black87,
              borderRadius: BorderRadius.circular(6),
            ),
            textStyle: const TextStyle(color: Colors.white, fontSize: 12),
            child: Container(
              // MAKE THE CIRCLE SMALLER
              width: 14,
              height: 14,
              decoration: const BoxDecoration(
                color: Colors.black87,
                shape: BoxShape.circle,
              ),
              child: const Center(
                child: Text(
                  "!",
                  style: TextStyle(
                    color: Colors.white,
                    fontSize: 10, // smaller font
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  /// All the columns used in the DataTable
  List<DataColumn> _buildDataColumns() {
    return [
      // A/B: Identification & Basic
      _colStat("Seat", "Which seat this player occupies at the table."),
      _colStat("Player ID", "The player's unique identifier."),
      _colStat("Nickname", "The player's nickname/alias."),

      _colStat("HP", "Hands Played: Total number of hands dealt to the player."),
      _colStat("HW", "Hands Won: Number of hands the player has won."),
      _colStat("HSF", "Hands Seen Flop: # of hands where they saw the flop."),
      _colStat("HST", "Hands Seen Turn: # of hands where they saw the turn."),
      _colStat("HSR", "Hands Seen River: # of hands where they saw the river."),
      _colStat("HSD", "Hands Saw Showdown: # of hands reaching showdown."),
      _colStat("Profit", "Total profit/loss in chips or currency."),
      _colStat("BigWin", "Biggest single pot the player has won."),
      _colStat("BigLost", "Biggest single pot the player has lost."),

      // C: Preflop Stats
      _colStat("f0Inv", "Folded preflop with 0 investment (no blinds posted)."),
      _colStat("VPIP", "Voluntarily Put \$ in Pot: # of times player calls/raises."),
      _colStat("PFR", "Preflop Raise: # of times player raises preflop."),
      _colStat("Limp", "Number of times player just calls the BB preflop."),
      _colStat("L-Fold", "Limp-Fold: # of times limps then folds to a raise."),
      _colStat("C.Call", "Cold-Call: # of times player calls a raise w/ no prior investment."),
      _colStat("3bet", "Number of 3-bets made preflop."),
      _colStat("4bet", "Number of 4-bets made preflop."),
      _colStat("5+bet", "Number of 5-bets or beyond preflop."),
      _colStat("f2-3b", "Fold to 3-bet: # of times folded facing a 3-bet."),
      _colStat("f2-4b", "Fold to 4-bet: # of times folded facing a 4-bet."),
      _colStat("StealA", "Steal Attempt: open-raise in late position vs blinds."),
      _colStat("f2Stl", "Fold to Steal: # of times folded against a steal attempt."),
      _colStat("3bvsStl", "3-bet vs Steal: # times 3-bet specifically vs a steal."),
      _colStat("avgOR", "Average Open-Raise Size."),
      _colStat("avg3b", "Average 3-bet Size."),
      _colStat("avg4b", "Average 4-bet Size."),
      _colStat("pfDist", "Preflop sizing distribution (map)."),
      _colStat("openR", "Open-Raise Count: # times player was first raiser preflop."),
      _colStat("fold2OR", "Fold to Open: # times folded vs an open-raise."),
      _colStat("f2-4b+", "Fold to 4-bet+: # times folded to 4-bet or higher."),
      _colStat("ORSamp", "Samples used in avgOpenRaiseSize."),
      _colStat("3bSamp", "Samples used in avgThreeBetSize."),
      _colStat("4bSamp", "Samples used in avgFourBetSize."),

      // D: Postflop
      _colStat("cBetF", "C-bet Flop: # times cont-bet on flop."),
      _colStat("cBetT", "C-bet Turn: # times cont-bet on turn."),
      _colStat("cBetR", "C-bet River: # times cont-bet on river."),
      _colStat("f2cBF", "Fold to Flop C-bet."),
      _colStat("f2cBT", "Fold to Turn C-bet."),
      _colStat("f2cBR", "Fold to River C-bet."),
      _colStat("rF", "Raise Flop: # times raised on flop."),
      _colStat("rT", "Raise Turn: # times raised on turn."),
      _colStat("rR", "Raise River: # times raised on river."),
      _colStat("xRFlop", "Check-Raise Flop: # times x-raised flop."),
      _colStat("xRTurn", "Check-Raise Turn: # times x-raised turn."),
      _colStat("xRRiver", "Check-Raise River: # times x-raised river."),
      _colStat("pfAggA", "Postflop Agg Actions: # bets/raises postflop."),
      _colStat("pfAggO", "Postflop Agg Opps: # opportunities to bet/raise postflop."),

      // E: Bet Sizing & Barrels
      _colStat("avgFB", "Average Flop Bet Size."),
      _colStat("avgTB", "Average Turn Bet Size."),
      _colStat("avgRB", "Average River Bet Size."),
      _colStat("bsDistF", "Flop Bet-Size Distribution."),
      _colStat("bsDistT", "Turn Bet-Size Distribution."),
      _colStat("bsDistR", "River Bet-Size Distribution."),
      _colStat("dblBar", "Double-Barrel: # times bet flop & turn."),
      _colStat("trpBar", "Triple-Barrel: # times bet flop, turn, river."),
      _colStat("f2DB", "Fold to Double-Barrel: folded on turn after calling flop."),
      _colStat("f2TB", "Fold to Triple-Barrel: folded on river after calling turn."),
      _colStat("ovrBet", "Overbet Count postflop."),
      _colStat("smBet", "Small Bet Count postflop (< X%)."),

      // F: Position & Multiway
      _colStat("PosStats", "Position-based stats breakdown."),
      _colStat("HU Pots", "Heads-Up Pots played."),
      _colStat("MW Pots", "Multiway Pots (3+ players)."),
      _colStat("f2CBetIP", "Fold to Flop C-bet in-position."),
      _colStat("f2CBetOOP", "Fold to Flop C-bet out-of-position."),

      // G: Showdown
      _colStat("WTSD", "Went to Showdown: # times called down to showdown."),
      _colStat("WSD", "Won at Showdown: # times won at showdown."),
      _colStat("Ww/oSD", "Won w/o Showdown: # times won before showdown."),
      _colStat("bluffC", "Bluff Catch: # times caught a bluff."),

      // H: All-In & EV
      _colStat("AllInEV", "All-In EV: EV in all-in spots."),
      _colStat("Act.WinAI", "Actual Winnings in all-in situations."),

      // I: Timing & Session
      _colStat("avgTpf", "Avg Time to Act Preflop."),
      _colStat("avgTpost", "Avg Time to Act Postflop."),
      _colStat("Sess#", "Total Sessions tracked."),
      _colStat("SessProf", "Session Profit."),
      _colStat("Notes", "Any free-form notes for the player."),

      // Computed stats (ratios)
      _colStat("VPIP %", "VPIP% = (VPIPCount / HandsPlayed) * 100."),
      _colStat("PFR %", "PFR% = (PFRCount / HandsPlayed) * 100."),
      _colStat("AggFreq%", "Aggression Frequency postflop."),
      _colStat("WTSD%", "Went To Showdown% = (wentToShowdown / handsSeenFlop)*100."),
      _colStat("W\$SD%", "Won \$ at Showdown% = (wonAtShowdown / wentToShowdown)*100."),
      _colStat("EV Diff", "All-In EV diff = (ActualWinningsAllIn - AllInEV)."),
    ];
  }

  DataRow _buildDataRow(int seatIndex, Player player, PlayerStats? s) {
    if (s == null) {
      return DataRow(
        cells: [
          _cell("Seat ${seatIndex + 1}"),
          _cell("No Stats"),
          _cell("N/A"),
          for (int i = 0; i < 80; i++) _cell("0"),
        ],
      );
    }

    return DataRow(
      cells: [
        _cell("Seat ${seatIndex + 1}"),
        _cell(s.playerId),
        _cell(s.nickname.isNotEmpty ? s.nickname : "P${seatIndex + 1}"),

        // Basic
        _cell(s.handsPlayed),
        _cell(s.handsWon),
        _cell(s.handsSeenFlop),
        _cell(s.handsSeenTurn),
        _cell(s.handsSeenRiver),
        _cell(s.handsSawShowdown),
        _cell(s.totalProfit),
        _cell(s.biggestPotWon),
        _cell(s.biggestPotLost),

        // Preflop
        _cell(s.foldedPreflopNoInvestCount),
        _cell(s.vpipCount),
        _cell(s.pfrCount),
        _cell(s.limpCount),
        _cell(s.limpFoldCount),
        _cell(s.coldCallCount),
        _cell(s.threeBetCount),
        _cell(s.fourBetCount),
        _cell(s.fivePlusBetCount),
        _cell(s.foldToThreeBetCount),
        _cell(s.foldToFourBetCount),
        _cell(s.stealAttemptCount),
        _cell(s.foldToStealCount),
        _cell(s.threeBetVsStealCount),
        _cell(s.avgOpenRaiseSize),
        _cell(s.avgThreeBetSize),
        _cell(s.avgFourBetSize),
        _cell(s.preflopSizingDistribution),
        _cell(s.openRaiseCount),
        _cell(s.foldToOpenCount),
        _cell(s.foldToFourBetPlusCount),
        _cell(s.openRaiseSamples),
        _cell(s.threeBetSamples),
        _cell(s.fourBetSamples),

        // Postflop
        _cell(s.cBetFlopCount),
        _cell(s.cBetTurnCount),
        _cell(s.cBetRiverCount),
        _cell(s.foldToFlopCBetCount),
        _cell(s.foldToTurnCBetCount),
        _cell(s.foldToRiverCBetCount),
        _cell(s.raiseFlopCount),
        _cell(s.raiseTurnCount),
        _cell(s.raiseRiverCount),
        _cell(s.checkRaiseFlopCount),
        _cell(s.checkRaiseTurnCount),
        _cell(s.checkRaiseRiverCount),
        _cell(s.postFlopAggActions),
        _cell(s.postFlopAggOpps),

        // Bet Sizing & Barrels
        _cell(s.avgFlopBetSize),
        _cell(s.avgTurnBetSize),
        _cell(s.avgRiverBetSize),
        _cell(s.betSizeDistributionFlop),
        _cell(s.betSizeDistributionTurn),
        _cell(s.betSizeDistributionRiver),
        _cell(s.doubleBarrelCount),
        _cell(s.tripleBarrelCount),
        _cell(s.foldToDoubleBarrelCount),
        _cell(s.foldToTripleBarrelCount),
        _cell(s.overbetCount),
        _cell(s.smallBetCount),

        // Position & Multiway
        _cell(s.statsByPosition),
        _cell(s.potsPlayedHeadsUp),
        _cell(s.potsPlayedMultiway),
        _cell(s.foldToFlopCBetIP),
        _cell(s.foldToFlopCBetOOP),

        // Showdown
        _cell(s.wentToShowdown),
        _cell(s.wonAtShowdown),
        _cell(s.wonWithoutShowdown),
        _cell(s.bluffCatchCount),

        // All-in & EV
        _cell(s.allInEV),
        _cell(s.actualWinningsWhenAllIn),

        // Timing & Sessions
        _cell(s.avgTimeToActPreflop),
        _cell(s.avgTimeToActPostflop),
        _cell(s.totalSessions),
        _cell(s.sessionProfit),
        _cell(s.notes.isNotEmpty ? s.notes : "N/A"),

        // Computed
        _cell("${(s.vpip * 100).toStringAsFixed(1)}%"),
        _cell("${(s.pfr * 100).toStringAsFixed(1)}%"),
        _cell("${(s.aggFreq * 100).toStringAsFixed(1)}%"),
        _cell("${(s.wtsd * 100).toStringAsFixed(1)}%"),
        _cell("${(s.w$sd * 100).toStringAsFixed(1)}%"),
        _cell(s.allInEVDiff.toStringAsFixed(2)),
      ],
    );
  }

  DataCell _cell(Object? value) {
    return DataCell(
      Text(
        value?.toString() ?? "0",
        style: const TextStyle(color: Colors.white, fontSize: 12),
      ),
    );
  }
}
