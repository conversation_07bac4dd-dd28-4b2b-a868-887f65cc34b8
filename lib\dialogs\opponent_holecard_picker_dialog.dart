import 'dart:async';
import 'dart:convert';
import 'package:flutter/material.dart';
import '../models.dart'; // your path to Player, CardModel, etc.
import 'show_centered_game_area_dialog.dart';
import 'override_dialog.dart';

/// Shows a dialog for picking exactly 2 cards for one opponent seat (or seat=0 if hero).
/// Returns the 2 chosen CardModel objects (or null if cancelled).
/// If the user clicks "Muck," we pop an empty list [] (i.e. a fold).
Future<List<CardModel>?> showOpponentHoleCardPickerDialog({
  required BuildContext context,
  required int seatIndex,
  required List<Player> players,
  required List<CardModel> Function() buildDeck,
  required Set<String> usedCards,
  required int? Function(CardModel card) findSeatHoldingCard,
  required CardModel Function(int seatIndex, CardModel oldCard) pickRandomCardForSeat,
}) async {
  // Decide the dialog title and confirm button label based on seatIndex:
  // If seatIndex == 0, we say "Pick your cards and deal" + "Deal"
  // Otherwise, "Pick 2 Cards for Player X" + "Confirm"
  final bool isHeroSeat = (seatIndex == 0);
  final String dialogTitle = isHeroSeat 
      ? "Pick your cards and deal" 
      : "Pick 2 Cards for Player ${seatIndex + 1}";
  final String confirmButtonLabel = isHeroSeat ? "Deal" : "Confirm";

  // We'll store the chosen 2 placeholders in a list of length 2.
  // If picks[i] is null, that means that slot is currently empty.
  List<CardModel?> picks = List.filled(2, null);

  // This variable tracks which of the two placeholder slots is "active."
  int activePlaceholder = 0;

  // We build a fresh deck from the provided function:
  final allCards = buildDeck();

  // We'll show a centered dialog over the game area
  return showCenteredGameAreaDialog<List<CardModel>?>(
    context: context,
    width: 600,
    height: 450,
    child: StatefulBuilder(
      builder: (dialogCtx, setStateSB) {
        // If both picks are chosen, we can enable the confirm button
        bool bothChosen = (picks[0] != null && picks[1] != null);

        // Helper function: automatically advance from slot 0 -> slot 1
        void _maybeAutoAdvance() {
          if (activePlaceholder < 1) {
            setStateSB(() => activePlaceholder++);
          }
        }

        // Called when user clicks one of the 52 cards in the grid
        void handleGridCardClick(CardModel chosenCard) {
          final chosenKey = '${chosenCard.rank}_${chosenCard.suit}';

          // If this card is already in picks, ignore
          for (var existingPick in picks) {
            if (existingPick != null) {
              final existingKey = '${existingPick.rank}_${existingPick.suit}';
              if (existingKey == chosenKey) return;
            }
          }

          // If that card is used somewhere else (community or another seat),
          // we show the overrideDialog before picking
          final isUsed = usedCards.contains(chosenKey);
          if (isUsed) {
            showOverrideDialog(
              dialogContext: dialogCtx,
              usedCard: chosenCard,
              onOverride: () {
                setStateSB(() {
                  picks[activePlaceholder] = chosenCard;
                  _maybeAutoAdvance();
                });
              },
            );
          } else {
            // Normal pick flow
            setStateSB(() {
              picks[activePlaceholder] = chosenCard;
              _maybeAutoAdvance();
            });
          }
        }

        // Remove the card from the currently active placeholder
        void handleRemoveCard() {
          final oldCard = picks[activePlaceholder];
          if (oldCard != null) {
            // Optionally do something else with oldCard
            picks[activePlaceholder] = null;
          }
          setStateSB(() {}); 
        }

        // === UI LAYOUT ===
        return Container(
          color: Colors.blueGrey.shade800,
          child: Column(
            children: [
              // TITLE BAR
              Container(
                height: 40,
                color: Colors.blueGrey.shade700,
                alignment: Alignment.centerLeft,
                padding: const EdgeInsets.only(left: 16),
                child: Row(
                  children: [
                    Text(
                      dialogTitle,  // <--- "Pick your cards and deal" for seat 0
                      style: const TextStyle(color: Colors.white),
                    ),
                    const Spacer(),
                    // If there's a card in the currently-active slot, show "Remove" button
                    if (picks[activePlaceholder] != null)
                      TextButton(
                        onPressed: handleRemoveCard,
                        child: const Text(
                          "Remove",
                          style: TextStyle(color: Colors.red),
                        ),
                      ),
                  ],
                ),
              ),

              const SizedBox(height: 8),

              // TOP ROW WITH OUR 2 PLACEHOLDERS
              Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: List.generate(2, (i) {
                  final pc = picks[i];
                  final isActive = (i == activePlaceholder);

                  return GestureDetector(
                    onTap: () => setStateSB(() => activePlaceholder = i),
                    child: Container(
                      width: 50,
                      height: 70,
                      margin: const EdgeInsets.all(4),
                      decoration: BoxDecoration(
                        color: isActive 
                          ? Colors.blueGrey.shade300 
                          : Colors.grey.shade300,
                        border: Border.all(
                          color: isActive ? Colors.blue : Colors.black45,
                          width: isActive ? 2 : 1,
                        ),
                      ),
                      child: (pc == null)
                          ? const Center(child: Icon(Icons.add))
                          : _buildCardFront(pc, width: 50, height: 70),
                    ),
                  );
                }),
              ),

              const SizedBox(height: 4),

              // 52-CARD GRID
              Expanded(
                child: GridView.builder(
                  gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
                    crossAxisCount: 13,   // 13 columns
                    childAspectRatio: 0.7,
                  ),
                  itemCount: allCards.length,
                  itemBuilder: (context, index) {
                    final c = allCards[index];
                    final cardKey = '${c.rank}_${c.suit}';

                    // Mark it as "used or chosen" if it's in usedCards or in picks
                    bool isUsedOrChosen = usedCards.contains(cardKey);
                    for (final pickCard in picks) {
                      if (pickCard != null) {
                        final pickKey = '${pickCard.rank}_${pickCard.suit}';
                        if (pickKey == cardKey) {
                          isUsedOrChosen = true;
                          break;
                        }
                      }
                    }

                    final finalOpacity = isUsedOrChosen ? 0.3 : 1.0;

                    return GestureDetector(
                      onTap: () => isUsedOrChosen 
                          ? null 
                          : handleGridCardClick(c),
                      child: AnimatedContainer(
                        duration: const Duration(milliseconds: 200),
                        margin: const EdgeInsets.all(4),
                        decoration: BoxDecoration(
                          border: Border.all(color: Colors.black54),
                          borderRadius: BorderRadius.circular(6),
                        ),
                        child: Opacity(
                          opacity: finalOpacity,
                          child: _buildCardFront(c, width: 45, height: 65),
                        ),
                      ),
                    );
                  },
                ),
              ),

              // BOTTOM ACTION BAR (Muck + Deal/Confirm)
              Container(
                height: 50,
                color: Colors.blueGrey.shade700,
                alignment: Alignment.centerRight,
                padding: const EdgeInsets.symmetric(horizontal: 16),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.end,
                  children: [
                    // Only show "Muck" if this is NOT the hero seat
                    if (!isHeroSeat)
                      ElevatedButton(
                        style: ElevatedButton.styleFrom(
                          backgroundColor: Colors.red,    // red background
                          foregroundColor: Colors.white,   // white text
                        ),
                        onPressed: () {
                          // Return an empty list => indicates "fold/muck"
                          Navigator.of(dialogCtx).pop(<CardModel>[]);
                        },
                        child: const Text("Muck"),
                      ),
                    if (!isHeroSeat) const SizedBox(width: 16),

                    // "Deal" if seat=0, else "Confirm"
                    ElevatedButton(
                      style: ElevatedButton.styleFrom(
                        backgroundColor: Colors.blueGrey.shade700,
                        foregroundColor: Colors.white,
                      ),
                      onPressed: bothChosen
                          ? () {
                              final chosen =
                                  picks.whereType<CardModel>().toList();
                              Navigator.of(dialogCtx).pop(chosen);
                            }
                          : null,
                      child: Text(confirmButtonLabel),
                    ),
                  ],
                ),
              ),
            ],
          ),
        );
      },
    ),
  );
}

/// Minimal card-front widget for displaying an individual CardModel.
Widget _buildCardFront(CardModel c, {double width = 60, double height = 80}) {
  final suitSymbol = (c.suit == "Hearts")
      ? "♥"
      : (c.suit == "Diamonds")
          ? "♦"
          : (c.suit == "Clubs")
              ? "♣"
              : (c.suit == "Spades")
                  ? "♠"
                  : "?";

  final color = (c.suit == "Hearts" || c.suit == "Diamonds")
      ? Colors.red
      : Colors.black;

  return ClipRRect(
    borderRadius: BorderRadius.circular(4),
    child: Container(
      width: width,
      height: height,
      color: Colors.white,
      child: Stack(
        children: [
          Positioned(
            top: 4,
            left: 4,
            child: Text(
              c.rank,
              style: TextStyle(
                fontSize: 14,
                fontWeight: FontWeight.bold,
                color: color,
              ),
            ),
          ),
          Positioned(
            top: 22,
            left: 8,
            child: Text(
              suitSymbol,
              style: TextStyle(fontSize: 14, color: color),
            ),
          ),
        ],
      ),
    ),
  );
}
