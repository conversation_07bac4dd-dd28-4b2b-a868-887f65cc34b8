import 'package:hive/hive.dart';

part 'player_stats.g.dart';

@HiveType(typeId: 2)
class PlayerStats extends HiveObject {
  // -------------------------------------------------------------------------
  // A. Player Identification & Core
  // -------------------------------------------------------------------------
  @Hive<PERSON>ield(0)
  String playerId;

  @HiveField(1)
  String nickname;

  @HiveField(3)
  DateTime? lastHandTimestamp;

  // -------------------------------------------------------------------------
  // B. Basic Counters
  // -------------------------------------------------------------------------
  @HiveField(4)
  int handsPlayed;

  @HiveField(5)
  int handsWon;

  @HiveField(6)
  int handsSeenFlop;
  @HiveField(7)
  int handsSeenTurn;
  @HiveField(8)
  int handsSeenRiver;
  @HiveField(9)
  int handsSawShowdown;

  @HiveField(10)
  double totalProfit;
  @HiveField(11)
  double biggestPotWon;
  @HiveField(12)
  double biggestPotLost;

  // -------------------------------------------------------------------------
  // C. Preflop Stats
  // -------------------------------------------------------------------------
  @HiveField(13)
  int vpipCount;

  /// “Any raise preflop” stat (often known as PFR).
  @HiveField(14)
  int pfrCount;

  @HiveField(15)
  int limpCount;

  @HiveField(16)
  int limpFoldCount;

  @HiveField(17)
  int coldCallCount;

  @HiveField(18)
  int threeBetCount;

  @HiveField(19)
  int fourBetCount;

  /// If your code lumps 5-bets and beyond here, you can rename it fivePlusBetCount
  @HiveField(20)
  int fivePlusBetCount;

  @HiveField(21)
  int foldToThreeBetCount;

  @HiveField(22)
  int foldToFourBetCount;

  @HiveField(23)
  int stealAttemptCount;

  @HiveField(24)
  int foldToStealCount;

  @HiveField(25)
  int threeBetVsStealCount;

  @HiveField(26)
  double avgOpenRaiseSize;
  @HiveField(27)
  double avgThreeBetSize;
  @HiveField(28)
  double avgFourBetSize;

  // HERE is the distribution map:
  @HiveField(29)
  Map<String, int>? preflopSizingDistribution;

  @HiveField(72)
  int openRaiseCount;           // specifically increments when _preflopRaiseCount==0
  @HiveField(73)
  int foldToOpenCount;          // increments if you fold with _preflopRaiseCount==1
  @HiveField(74)
  int foldToFourBetPlusCount;   // increments if _preflopRaiseCount >= 3

  // -------------------------------------------------------------------------
  // ADDITIONAL FIELDS FOR TRACKING RAISE-SIZE SAMPLES
  // -------------------------------------------------------------------------
  @HiveField(75)
  int openRaiseSamples;

  @HiveField(76)
  int threeBetSamples;

  @HiveField(77)
  int fourBetSamples;

  @HiveField(78)
  int foldedPreflopNoInvestCount;

  // -------------------------------------------------------------------------
  // D. Postflop Street-by-Street Stats
  // -------------------------------------------------------------------------
  @HiveField(30)
  int cBetFlopCount;
  @HiveField(31)
  int cBetTurnCount;
  @HiveField(32)
  int cBetRiverCount;

  @HiveField(33)
  int foldToFlopCBetCount;
  @HiveField(34)
  int foldToTurnCBetCount;
  @HiveField(35)
  int foldToRiverCBetCount;

  @HiveField(36)
  int raiseFlopCount;
  @HiveField(37)
  int raiseTurnCount;
  @HiveField(38)
  int raiseRiverCount;

  @HiveField(39)
  int checkRaiseFlopCount;
  @HiveField(40)
  int checkRaiseTurnCount;
  @HiveField(41)
  int checkRaiseRiverCount;

  @HiveField(42)
  int postFlopAggActions;
  @HiveField(43)
  int postFlopAggOpps;

  // -------------------------------------------------------------------------
  // E. Bet Sizing & Barrels
  // -------------------------------------------------------------------------
  @HiveField(44)
  double avgFlopBetSize;
  @HiveField(45)
  double avgTurnBetSize;
  @HiveField(46)
  double avgRiverBetSize;

  @HiveField(47)
  Map<String, int>? betSizeDistributionFlop;
  @HiveField(48)
  Map<String, int>? betSizeDistributionTurn;
  @HiveField(49)
  Map<String, int>? betSizeDistributionRiver;

  @HiveField(50)
  int doubleBarrelCount;
  @HiveField(51)
  int tripleBarrelCount;
  @HiveField(52)
  int foldToDoubleBarrelCount;
  @HiveField(53)
  int foldToTripleBarrelCount;

  @HiveField(54)
  int overbetCount;
  @HiveField(55)
  int smallBetCount;

  // -------------------------------------------------------------------------
  // F. Position & Multiway
  // -------------------------------------------------------------------------
  @HiveField(56)
  Map<String, Map<String, int>>? statsByPosition;

  @HiveField(57)
  int potsPlayedHeadsUp;
  @HiveField(58)
  int potsPlayedMultiway;

  @HiveField(59)
  int foldToFlopCBetIP;
  @HiveField(60)
  int foldToFlopCBetOOP;

  // -------------------------------------------------------------------------
  // G. Showdown & Non-Showdown
  // -------------------------------------------------------------------------
  @HiveField(61)
  int wentToShowdown;
  @HiveField(62)
  int wonAtShowdown;
  @HiveField(63)
  int wonWithoutShowdown;

  @HiveField(64)
  int bluffCatchCount;

  // -------------------------------------------------------------------------
  // H. All-In & EV
  // -------------------------------------------------------------------------
  @HiveField(65)
  double allInEV;

  @HiveField(66)
  double actualWinningsWhenAllIn;

  // -------------------------------------------------------------------------
  // I. Timing, Session, Misc
  // -------------------------------------------------------------------------
  @HiveField(67)
  double avgTimeToActPreflop;
  @HiveField(68)
  double avgTimeToActPostflop;
  @HiveField(69)
  int totalSessions;
  @HiveField(70)
  double sessionProfit;

  @HiveField(71)
  String notes;

  // -------------------------------------------------------------------------
  // J. Constructor
  // -------------------------------------------------------------------------
  PlayerStats({
    required this.playerId,
    this.nickname = '',
    this.lastHandTimestamp,

    // -- Basic
    this.handsPlayed = 0,
    this.handsWon = 0,
    this.handsSeenFlop = 0,
    this.handsSeenTurn = 0,
    this.handsSeenRiver = 0,
    this.handsSawShowdown = 0,
    this.totalProfit = 0.0,
    this.biggestPotWon = 0.0,
    this.biggestPotLost = 0.0,

    // -- Preflop
    this.foldedPreflopNoInvestCount = 0,  // also in constructor
    this.vpipCount = 0,
    this.pfrCount = 0,
    this.limpCount = 0,
    this.limpFoldCount = 0,
    this.coldCallCount = 0,
    this.threeBetCount = 0,
    this.fourBetCount = 0,
    this.fivePlusBetCount = 0,
    this.foldToThreeBetCount = 0,
    this.foldToFourBetCount = 0,
    this.stealAttemptCount = 0,
    this.foldToStealCount = 0,
    this.threeBetVsStealCount = 0,
    this.avgOpenRaiseSize = 0.0,
    this.avgThreeBetSize = 0.0,
    this.avgFourBetSize = 0.0,
    this.preflopSizingDistribution,

    // -- Additional new counters
    this.openRaiseCount = 0,
    this.foldToOpenCount = 0,
    this.foldToFourBetPlusCount = 0,

    this.openRaiseSamples = 0,
    this.threeBetSamples = 0,
    this.fourBetSamples = 0,

    // -- Postflop
    this.cBetFlopCount = 0,
    this.cBetTurnCount = 0,
    this.cBetRiverCount = 0,
    this.foldToFlopCBetCount = 0,
    this.foldToTurnCBetCount = 0,
    this.foldToRiverCBetCount = 0,
    this.raiseFlopCount = 0,
    this.raiseTurnCount = 0,
    this.raiseRiverCount = 0,
    this.checkRaiseFlopCount = 0,
    this.checkRaiseTurnCount = 0,
    this.checkRaiseRiverCount = 0,
    this.postFlopAggActions = 0,
    this.postFlopAggOpps = 0,

    // -- Bet Sizing & Barrels
    this.avgFlopBetSize = 0.0,
    this.avgTurnBetSize = 0.0,
    this.avgRiverBetSize = 0.0,
    this.betSizeDistributionFlop,
    this.betSizeDistributionTurn,
    this.betSizeDistributionRiver,
    this.doubleBarrelCount = 0,
    this.tripleBarrelCount = 0,
    this.foldToDoubleBarrelCount = 0,
    this.foldToTripleBarrelCount = 0,
    this.overbetCount = 0,
    this.smallBetCount = 0,

    // -- Position & Multiway
    this.statsByPosition,
    this.potsPlayedHeadsUp = 0,
    this.potsPlayedMultiway = 0,
    this.foldToFlopCBetIP = 0,
    this.foldToFlopCBetOOP = 0,

    // -- Showdown
    this.wentToShowdown = 0,
    this.wonAtShowdown = 0,
    this.wonWithoutShowdown = 0,
    this.bluffCatchCount = 0,

    // -- All-in & EV
    this.allInEV = 0.0,
    this.actualWinningsWhenAllIn = 0.0,

    // -- Timing & Session
    this.avgTimeToActPreflop = 0.0,
    this.avgTimeToActPostflop = 0.0,
    this.totalSessions = 0,
    this.sessionProfit = 0.0,

    // -- Misc
    this.notes = '',
  });

  // -------------------------------------------------------------------------
  // K. Example Computed Getters
  // -------------------------------------------------------------------------
  double get vpip => handsPlayed > 0 ? vpipCount / handsPlayed : 0.0;
  double get pfr => handsPlayed > 0 ? pfrCount / handsPlayed : 0.0;
  double get aggFreq => postFlopAggOpps > 0
      ? postFlopAggActions / postFlopAggOpps
      : 0.0;
  double get wtsd => handsSeenFlop > 0
      ? (wentToShowdown / handsSeenFlop)
      : 0.0;
  double get w$sd => wentToShowdown > 0
      ? (wonAtShowdown / wentToShowdown)
      : 0.0;

  // All-in EV difference
  double get allInEVDiff => actualWinningsWhenAllIn - allInEV;

  // -------------------------------------------------------------------------
  // Example Computed Getters: Postflop Bet Sizing & Barrels
  // -------------------------------------------------------------------------

  int get totalFlopBets =>
      betSizeDistributionFlop
          ?.values
          .fold<int>(0, (int sum, int count) => sum + count)
      ?? 0;

  int get totalTurnBets =>
      betSizeDistributionTurn
          ?.values
          .fold<int>(0, (int sum, int count) => sum + count)
      ?? 0;

  int get totalRiverBets =>
      betSizeDistributionRiver
          ?.values
          .fold<int>(0, (int sum, int count) => sum + count)
      ?? 0;

  Map<String, double> get betSizeDistributionFlopPercentages {
    final total = totalFlopBets;
    if (total == 0 || betSizeDistributionFlop == null) return {};
    return betSizeDistributionFlop!.map((key, count) {
      return MapEntry(key, count / total);
    });
  }

  Map<String, double> get betSizeDistributionTurnPercentages {
    final total = totalTurnBets;
    if (total == 0 || betSizeDistributionTurn == null) return {};
    return betSizeDistributionTurn!.map((key, count) {
      return MapEntry(key, count / total);
    });
  }

  Map<String, double> get betSizeDistributionRiverPercentages {
    final total = totalRiverBets;
    if (total == 0 || betSizeDistributionRiver == null) return {};
    return betSizeDistributionRiver!.map((key, count) {
      return MapEntry(key, count / total);
    });
  }

  double get doubleBarrelRate {
    if (cBetFlopCount == 0) return 0.0;
    return doubleBarrelCount / cBetFlopCount;
  }

  double get tripleBarrelRate {
    if (cBetFlopCount == 0) return 0.0;
    return tripleBarrelCount / cBetFlopCount;
  }

  double get foldToDoubleBarrelRate {
    if (cBetTurnCount == 0) return 0.0;
    return foldToDoubleBarrelCount / cBetTurnCount;
  }

  double get foldToTripleBarrelRate {
    if (cBetRiverCount == 0) return 0.0;
    return foldToTripleBarrelCount / cBetRiverCount;
  }

  double get overbetFrequency {
    if (postFlopAggActions == 0) return 0.0;
    return overbetCount / postFlopAggActions;
  }

  double get smallBetFrequency {
    if (postFlopAggActions == 0) return 0.0;
    return smallBetCount / postFlopAggActions;
  }

  double get averageFlopBetSize => avgFlopBetSize;
  double get averageTurnBetSize => avgTurnBetSize;
  double get averageRiverBetSize => avgRiverBetSize;
}
