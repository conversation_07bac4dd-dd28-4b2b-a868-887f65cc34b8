import 'package:hive/hive.dart';

part 'poker_table.g.dart';

@HiveType(typeId: 10) // pick an unused typeId
class PokerTable extends HiveObject {
  /// Unique ID for this table.
  @HiveField(0)
  String tableId;

  /// Is this a tournament table or a cash game?
  @HiveField(1)
  bool isTournament;

  /// If it’s a tournament, store some details.
  @HiveField(2)
  DateTime? tournamentStartTime;

  @HiveField(3)
  double? prizePool;

  /// If it's a cash game, you might have a min/max buy-in, rake, etc.
  @HiveField(4)
  double minBuyIn;

  @HiveField(5)
  double maxBuyIn;

  /// Common rules for both:
  @HiveField(6)
  int seatCount; // e.g. 2..9

  @HiveField(7)
  int smallBlind; 

  @HiveField(8)
  int bigBlind;

  /// Additional advanced rules or flags (for both Tournaments & Cash):
  @HiveField(9)
  bool hasAntes;

  @HiveField(10)
  int anteAmount;

  /// This map links seat indices to a *playerId*, which is the unique
  /// identifier for your `PlayerSuperStats` or whichever player record.
  ///
  /// Example usage:
  /// ```dart
  /// // If seat #2 belongs to "playerId_ABC123":
  /// table.seatMap[2] = "playerId_ABC123";
  /// // If seat is empty or nobody is assigned:
  /// table.seatMap[2] = null;
  /// ```
  @HiveField(11)
  Map<int, String?> seatMap;

  /// Any notes or custom rules
  @HiveField(12)
  String notes;

  // --------------------------------------------------------------
  // New or extended fields specifically for Tournaments:
  // --------------------------------------------------------------

  /// Total number of entrants in the tournament (for prize distribution).
  @HiveField(13)
  int totalEntrants;

  /// Number of remaining players (if you track it).
  @HiveField(14)
  int remainingPlayers;

  /// Number of paid positions, e.g. top 15% or fixed spots.
  @HiveField(15)
  int placesPaid;

  /// Dynamic payout structure, e.g. {1: 40%, 2: 25%, 3: 15%, 4: 10% ...}
  @HiveField(16)
  Map<int, double>? payoutStructure;

  /// The current blind level (in a multi-level structure).
  @HiveField(17)
  int currentLevel;

  /// When does the *next* blind level start?
  @HiveField(18)
  DateTime? nextLevelStartTime;

  /// If re-entries are allowed, specify how many or whether it's unlimited.
  @HiveField(19)
  bool reentryAllowed;

  @HiveField(20)
  int maxReentries; // e.g. 1, 2, or unlimited (could use -1 for unlimited)

  /// The cutoff time for re-entries (or rebuys).
  @HiveField(21)
  DateTime? reentryCutoffTime;

  /// If add-ons are allowed, specify until which level or time.
  @HiveField(22)
  bool addOnAllowed;

  @HiveField(23)
  DateTime? addOnCutoffTime;

  /// Is the tournament in or near the bubble phase?
  @HiveField(24)
  bool isBubble;

  /// Could be any additional numerical factor for bubble pressure decisions.
  /// For example, an ICM ratio or some “bubbleFactor” multiplier.
  @HiveField(25)
  double bubbleFactor;

  /// Is there a shot-clock/time-bank system in place?
  @HiveField(26)
  bool useShotClock;

  @HiveField(27)
  int timeBankSeconds; // how many total seconds a player has in time bank?

  // (Optional) If you want to store a "startingChipAmount" for tournaments:
  // @HiveField(28)
  // int? startingChipAmount;

  // (Optional) If you want to store the "tableState" or anything else:
  // @HiveField(29)
  // String? tableState;

  PokerTable({
    required this.tableId,
    required this.isTournament,
    this.tournamentStartTime,
    this.prizePool,
    this.minBuyIn = 0,
    this.maxBuyIn = 0,
    required this.seatCount,
    this.smallBlind = 100,
    this.bigBlind = 200,
    this.hasAntes = false,
    this.anteAmount = 0,
    Map<int, String?>? seatMap,
    this.notes = '',

    // New tournament fields
    this.totalEntrants = 0,
    this.remainingPlayers = 0,
    this.placesPaid = 0,
    this.payoutStructure,
    this.currentLevel = 1,
    this.nextLevelStartTime,
    this.reentryAllowed = false,
    this.maxReentries = 0,
    this.reentryCutoffTime,
    this.addOnAllowed = false,
    this.addOnCutoffTime,
    this.isBubble = false,
    this.bubbleFactor = 1.0,
    this.useShotClock = false,
    this.timeBankSeconds = 0,
  }) : seatMap = seatMap ?? {};
}
