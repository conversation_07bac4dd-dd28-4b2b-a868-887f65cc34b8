<!DOCTYPE html>
<html>
<head>
  <!-- IMPORTANT: This ensures relative loading of Flutter assets and supports hash-based routing. -->
  <base href="/">

  <meta charset="UTF-8">
  <meta content="IE=Edge" http-equiv="X-UA-Compatible">
  <meta name="description" content="Join the best online multiplayer poker room!">

  <!-- iOS meta tags & icons -->
  <meta name="mobile-web-app-capable" content="yes">
  <meta name="apple-mobile-web-app-status-bar-style" content="black">
  <meta name="apple-mobile-web-app-title" content="4LeafPoker">

  <link rel="apple-touch-icon" href="icons/Icon-192.png">
  <link rel="icon" type="image/png" href="favicon.png"/>

  <title>4LeafPoker</title>
  <link rel="manifest" href="manifest.json">
</head>
<body>
  <!-- This is where Flutter loads your compiled app JS. -->
  <script src="flutter_bootstrap.js" async></script>
</body>
</html>
