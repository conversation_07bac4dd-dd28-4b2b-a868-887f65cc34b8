import 'dart:convert';
import 'package:http/http.dart' as http;
import 'package:four_leaf_poker/config_service.dart';

class Api {
  static String get baseUrl => ConfigService.baseUrl;

  static Future<Map<String, String>> openNewTable({
    required String name,
    required String blindLevel,
    required String startingStack,
    required int seatCount,
    String? seatPosition,
    required bool canJoinAfterStart,
    required bool rebuysActive,
    required String rebuyCount,
    int? turnDurationMs,
    required int dealerStart,
    // NEW: add sbCost / bbCost
    int? sbCost,
    int? bbCost,
  }) async {
    final url = Uri.parse('$baseUrl/OpenNewTable');

    // Build request body
    final body = {
      'name': name,
      'blindLevel': blindLevel,
      'startingStack': startingStack,
      'seatCount': seatCount.toString(),
      'canJoinAfterStart': canJoinAfterStart,
      'rebuysActive': rebuysActive,
      'rebuyCount': rebuyCount,
      'turnDurationMs': turnDurationMs,
      'dealerStart':      dealerStart.toString(),
    };

    if (seatPosition != null) {
      body['seatPosition'] = seatPosition;
    }

    // If sbCost or bbCost is provided, add them to body
    if (sbCost != null) {
      body['sbCost'] = sbCost.toString();
    }
    if (bbCost != null) {
      body['bbCost'] = bbCost.toString();
    }

    final response = await http.post(
      url,
      headers: {'Content-Type': 'application/json'},
      body: jsonEncode(body),
    );

    if (response.statusCode == 200) {
      final data = jsonDecode(response.body) as Map<String, dynamic>;
      final tableId = data['tableId'];
      return {
        'tableId': tableId,
      };
    } else {
      String errorMessage = 'Failed to create table. Status ${response.statusCode}';
      try {
        final json = jsonDecode(response.body);
        if (json is Map && json.containsKey('error')) {
          errorMessage = json['error'] as String;
        }
      } catch (_) {
        // Keep fallback message
      }
      throw Exception(errorMessage);
    }
  }

  // --------------------------------------------
  // GET TABLE DATA
  // --------------------------------------------
  static Future<Map<String, dynamic>> getTableData(String roomId) async {
    final url = Uri.parse('$baseUrl/GetTableData?roomId=$roomId');
    final response = await http.get(url);

    if (response.statusCode == 200) {
      return jsonDecode(response.body) as Map<String, dynamic>;
    } else {
      String errorMessage = 'Failed to get table data. Status ${response.statusCode}';
      try {
        final json = jsonDecode(response.body);
        if (json is Map && json.containsKey('error')) {
          errorMessage = json['error'] as String;
        }
      } catch (_) {}
      throw Exception(errorMessage);
    }
  }

  // --------------------------------------------
  // JOIN TABLE
  // --------------------------------------------
  static Future<Map<String, dynamic>> joinTable({
    required String tableId,
    required String name,
    required int seatPosition,
  }) async {
    final url = Uri.parse('$baseUrl/JoinTable');
    
    final body = {
      'tableId': tableId,
      'name': name,
      'seatPosition': seatPosition.toString(),
    };

    final response = await http.post(
      url,
      headers: {'Content-Type': 'application/json'},
      body: jsonEncode(body),
    );

    if (response.statusCode == 200) {
      return jsonDecode(response.body) as Map<String, dynamic>;
    } else {
      String errorMessage = 'Failed to join table. Status ${response.statusCode}';
      try {
        final json = jsonDecode(response.body);
        if (json is Map && json.containsKey('error')) {
          errorMessage = json['error'] as String;
        }
      } catch (_) {}
      throw Exception(errorMessage);
    }
  }
}
