import 'package:flutter/material.dart';
import '../models.dart'; // Your path to CardModel/Player/etc.
import 'show_centered_game_area_dialog.dart';

/// A small confirmation dialog for overriding a used card.
/// It calls showCenteredGameAreaDialog to present itself.
void showOverrideDialog({
  required BuildContext dialogContext,
  required CardModel usedCard,  // optional if you show its rank/suit in text
  required VoidCallback onOverride,
}) {
  showCenteredGameAreaDialog<void>(
    context: dialogContext,
    width: 400,
    height: 200,
    child: StatefulBuilder(
      builder: (dialogCtx, setStateSB) {
        return Container(
          color: Colors.blueGrey.shade800,
          child: Column(
            children: [
              Container(
                height: 40,
                color: Colors.red,
                alignment: Alignment.center,
                child: const Text(
                  "Override?",
                  style: TextStyle(color: Colors.white, fontSize: 16),
                ),
              ),
              Expanded(
                child: Center(
                  child: Text(
                    "This card is already in a player's hand.\n"
                    "Do you want to override ... ?",
                    textAlign: TextAlign.center,
                    style: const TextStyle(color: Colors.white70),
                  ),
                ),
              ),
              Container(
                color: Colors.blueGrey.shade700,
                padding:
                    const EdgeInsets.symmetric(vertical: 6, horizontal: 16),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.end,
                  children: [
                    ElevatedButton(
                      style:
                          ElevatedButton.styleFrom(backgroundColor: Colors.red),
                      onPressed: () {
                        Navigator.of(dialogCtx).pop(); // close this dialog
                        onOverride(); // perform actual override
                      },
                      child: const Text("Yes, override"),
                    ),
                  ],
                ),
              ),
            ],
          ),
        );
      },
    ),
  );
}
