import 'dart:math' as math;

/// A collection of "personality" parameters for each AI player.
class AiPersonality {
  /// 1–10 scale indicating how likely they are to raise or re-raise.
  final double aggressiveness;

  /// 1–5 scale indicating how wide they play (1 = very tight, 5 = very loose).
  final int looseness;

  /// Probability (0–1) of bluffing in certain spots.
  final double bluffFrequency;

  /// Probability (0–1) of folding marginal spots if equity is below certain threshold.
  final double riskAversion;

  /// Example: 0.0–0.5 indicates how “tilted” or emotionally unbalanced they start.
  double tiltFactor;

  AiPersonality({
    required this.aggressiveness,
    required this.looseness,
    required this.bluffFrequency,
    required this.riskAversion,
    required this.tiltFactor,
  });

  /// Generate random personality parameters
  factory AiPersonality.random() {
    final rng = math.Random();
    return AiPersonality(
      aggressiveness: (rng.nextDouble() * 9.0) + 1.0,   // 1.0 to 10.0
      looseness: rng.nextInt(5) + 1,                   // 1 to 5
      bluffFrequency: rng.nextDouble() * 0.3,          // up to 30% bluff freq
      riskAversion: rng.nextDouble() * 0.6,            // 0.0 to 0.6
      tiltFactor: rng.nextDouble() * 0.1,              // up to 0.1 tilt
    );
  }
}
