import 'package:flutter/material.dart';
import '../../action_history.dart';
import 'header_component.dart';
import 'body_component.dart';
import 'footer_component.dart';

class ActionLogModal extends StatefulWidget {
  final HistoryPanelState panelState;
  final VoidCallback onPanelToggle;
  final List<String> handHistory;
  final ScrollController historyScrollController;
  final VoidCallback onCopyHistory;
  final VoidCallback? onSwapCardView;
  final VoidCallback? onExitTable;

  const ActionLogModal({
    Key? key,
    required this.panelState,
    required this.onPanelToggle,
    required this.handHistory,
    required this.historyScrollController,
    required this.onCopyHistory,
    this.onSwapCardView,
    this.onExitTable,
  }) : super(key: key);

  @override
  _ActionLogModalState createState() => _ActionLogModalState();
}

class _ActionLogModalState extends State<ActionLogModal> {
  bool action_log_opened = false;

  void toggleActionLog() {
    setState(() {
      print("Toggling action log...  $action_log_opened");
      action_log_opened = !action_log_opened;
    });
  }

  @override
  Widget build(BuildContext context) {
        final bool isMobile = MediaQuery.of(context).size.width < 600;

    return AnimatedContainer(
      width: 302,
      duration: Duration(milliseconds: 200),
       height: action_log_opened? 1000: 100,
      padding: EdgeInsets.symmetric(horizontal: 16, vertical: 2),
      decoration: BoxDecoration(
        color: Color(0xFF004447),
        borderRadius: BorderRadius.circular(24),
        border: Border.all(color: Color(0xCC000000), width: 2.5),
      ),
      child: Column(
        mainAxisSize: MainAxisSize.max,
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        crossAxisAlignment: CrossAxisAlignment.stretch,
        children: [
          Align(
            alignment: Alignment.topCenter,
            child: InkWell(
              child: Icon(
                Icons.menu,
                color: Colors.black,
                size: 12,
              ),
            ),
          ),
          SizedBox(height: 16),
          HeaderComponent(
            onMenuTap: toggleActionLog,
            actionLogOpened: action_log_opened,
          ),
          if (action_log_opened) ...[
            const SizedBox(height: 16),
            Expanded(child: 
                          Expanded(
                child: AnimatedSwitcher(
                  duration: const Duration(milliseconds: 200),
                  child: _buildContentForState(widget.panelState, isMobile),
                ),
              )
            ),
            const SizedBox(height: 16),
            const FooterComponent(),
          ],
         SizedBox(height: 16),

        ],
      ),
    );
  }

  Widget _buildContentForState(HistoryPanelState state, bool isMobile) {
    switch (state) {
      case HistoryPanelState.closed:
        return const SizedBox.shrink();
      case HistoryPanelState.semiOpen:
        return _buildSemiOpenHistory(isMobile);
      case HistoryPanelState.fullyOpen:
        return _buildFullyOpenHistory();
    }
  }

  Widget _buildSemiOpenHistory(bool isMobile) {
    final displayed = takeLast(widget.handHistory, isMobile ? 2 : 5);
    return Stack(
      children: [
        Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: List.generate(displayed.length, (index) {
            final isLast = index == displayed.length - 1;
            final bgColor = (index % 2 == 0)
                ? Colors.blueGrey.shade800
                : Colors.blueGrey.shade600;
            return Container(
              decoration: BoxDecoration(
                color: bgColor,
                borderRadius: isLast
                    ? const BorderRadius.only(
                        bottomLeft: Radius.circular(6),
                        bottomRight: Radius.circular(6),
                      )
                    : null,
              ),
              padding: const EdgeInsets.symmetric(horizontal: 4, vertical: 0),
              child: Text(
                displayed[index],
                style: const TextStyle(color: Colors.white70, fontSize: 12),
                overflow: TextOverflow.ellipsis,
              ),
            );
          }),
        ),
        Positioned(
          top: 0,
          left: 0,
          right: 0,
          height: isMobile ? 5 : 50,
          child: Container(
            decoration: BoxDecoration(
              gradient: LinearGradient(
                begin: Alignment.topCenter,
                end: Alignment.bottomCenter,
                colors: [
                  Colors.blueGrey.shade900,
                  Colors.transparent,
                ],
              ),
            ),
          ),
        ),
      ],
    );
  }
  Widget _buildFullyOpenHistory() {
    return Scrollbar(
      thumbVisibility: true,
      controller: widget.historyScrollController,
      child: ListView.builder(
        controller: widget.historyScrollController,
        itemCount: widget.handHistory.length,
        itemBuilder: (context, index) {
          final bgColor = (index % 2 == 0)
              ? Colors.blueGrey.shade800
              : Colors.blueGrey.shade600;
          return Container(
            color: bgColor,
            padding: const EdgeInsets.symmetric(horizontal: 4, vertical: 0),
            child: Text(
              widget.handHistory[index],
              style: const TextStyle(color: Colors.white70, fontSize: 12),
            ),
          );
        },
      ),
    );
  }

  List<String> takeLast(List<String> list, int n) {
    if (n <= 0) return [];
    if (n >= list.length) return list;
    return list.sublist(list.length - n);
  }
}
