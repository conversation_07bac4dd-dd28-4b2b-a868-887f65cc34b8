import 'dart:math' as math;
import 'package:flutter/material.dart';

/// A generic helper that shows a modal in the center of your table area,
/// offset from the side panel. You can use it for any custom child widget.
Future<T?> showCenteredGameAreaDialog<T>({
  required BuildContext context,
  required Widget child,
  double width = 600,
  double height = 400,
}) {
  // We assume your side panel is ~300px wide; adjust if needed.
  final media = MediaQuery.of(context);
  final gameAreaWidth = media.size.width - 300; 
  final gameAreaHeight = media.size.height;

  final dialogWidth = math.min(width, gameAreaWidth * 0.9);
  final dialogHeight = math.min(height, gameAreaHeight * 0.9);

  final left = 300 + (gameAreaWidth - dialogWidth) / 2;
  final top = (gameAreaHeight - dialogHeight) / 2;

  return showGeneralDialog<T>(
    context: context,
    barrierDismissible: false,   // user cannot tap outside to dismiss
    barrierLabel: '',
    barrierColor: Colors.black54, 
    pageBuilder: (ctx, anim1, anim2) {
      return Stack(
        children: [
          Positioned(
            left: left,
            top: top,
            width: dialogWidth,
            height: dialogHeight,
            child: Material(
              color: Colors.blueGrey.shade800,
              elevation: 10,
              child: child,
            ),
          ),
        ],
      );
    },
  );
}
