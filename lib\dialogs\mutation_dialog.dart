import 'dart:math'; // For pi, sin, cos
import 'package:flutter/material.dart';
import 'package:flutter/services.dart'; // For Clipboard.setData
import 'package:hive/hive.dart';
import 'package:four_leaf_poker/dialogs/show_centered_game_area_dialog.dart';
import 'package:four_leaf_poker/hive/hand_record.dart';

/// Displays the Mutations dialog centered over the game area,
/// showing historical hands from Hive (not from a current in-memory hand).
Future<void> showMutationsDialog(BuildContext context) async {
  final box = Hive.box<HandRecord>('hand_records');
  final allHands = box.values.toList(); // This is List<HandRecord>
  int selectedIndex = 0;

  final scrollController = ScrollController();

  // We use Flutter's StatefulBuilder to manage our internal state for the dialog.
  showCenteredGameAreaDialog(
    context: context,
    width: 800,
    height: 600,
    child: StatefulBuilder(
      builder: (BuildContext context, StateSetter setState) {
        return ScrollConfiguration(
          // If you want to hide the default scrollbars, you can replace with your own behavior:
          behavior: const ScrollBehavior(),
          child: Container(
            width: 600,
            height: 400,
            color: Colors.blueGrey.shade800,
            child: Row(
              children: [
                // Left list of hands
                Container(
                  width: 120,
                  color: Colors.blueGrey.shade800,
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      // Center the "Mutations" label
                      Container(
                        padding: const EdgeInsets.all(8.0),
                        child: const Center(
                          child: Text(
                            "Mutations",
                            style: TextStyle(
                              color: Colors.lightGreenAccent,
                              fontSize: 14,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                        ),
                      ),
                      Expanded(
                        child: Scrollbar(
                          thumbVisibility: true,
                          controller: scrollController,
                          scrollbarOrientation: ScrollbarOrientation.left,
                          child: SingleChildScrollView(
                            controller: scrollController,
                            child: Column(
                              children: List.generate(allHands.length, (index) {
                                final hiveRecord = allHands[index];
                                final isSelected = (index == selectedIndex);
                                final shortLabel = "Hand ${index + 1}\n"
                                    "${hiveRecord.timestamp.toLocal().toString().split('.')[0]}";
                                return InkWell(
                                  onTap: () => setState(() {
                                    selectedIndex = index;
                                  }),
                                  child: Container(
                                    width: double.infinity,
                                    color: isSelected
                                        ? Colors.blueGrey.shade600
                                        : Colors.blueGrey.shade800,
                                    padding: const EdgeInsets.symmetric(
                                      vertical: 8,
                                      horizontal: 4,
                                    ),
                                    child: Text(
                                      shortLabel,
                                      textAlign: TextAlign.left,
                                      style: TextStyle(
                                        color: isSelected
                                            ? Colors.white
                                            : Colors.black87,
                                        fontSize: 12,
                                      ),
                                    ),
                                  ),
                                );
                              }),
                            ),
                          ),
                        ),
                      ),
                    ],
                  ),
                ),

                // Right detail
                Expanded(
                  child: Container(
                    color: Colors.blueGrey.shade800,
                    padding: const EdgeInsets.all(8),
                    child: allHands.isEmpty
                        ? const Center(
                            child: Text(
                              "No hands found in Hive",
                              style: TextStyle(color: Colors.white),
                            ),
                          )
                        : _buildHandRecordDetailView(
                            allHands[selectedIndex],
                            context,
                          ),
                  ),
                ),
              ],
            ),
          ),
        );
      },
    ),
  );
}

/// Builds the entire right-hand detail view:
/// Top mini-table + bottom textual details.
Widget _buildHandRecordDetailView(HandRecord hiveRecord, BuildContext context) {
  return Column(
    children: [
      // 1) The new simplified mini-table on top
      _buildMiniOvalPokerTable(hiveRecord),

      const SizedBox(height: 8),

      // 2) The expanded scrollable detail
      Expanded(
        child: SingleChildScrollView(
          child: _buildHandRecordDetailBottom(hiveRecord, context),
        ),
      ),
    ],
  );
}

/// Builds the new mini poker table with:
/// - A green oval in the center
/// - 9 seat circles around it
/// - The board runout in the very center
Widget _buildMiniOvalPokerTable(HandRecord hiveRecord) {
  // Outer “canvas” size for the mini-table area
  const double outerWidth = 520;
  const double outerHeight = 320;

  // Size of the green oval table in the center
  const double tableWidth = 260;
  const double tableHeight = 140;

  return SizedBox(
    width: outerWidth,
    height: outerHeight,
    child: Stack(
      alignment: Alignment.center,
      children: [
        // 1) The green oval
        Container(
          width: tableWidth,
          height: tableHeight,
          decoration: BoxDecoration(
            color: Colors.green.shade800,
            borderRadius: BorderRadius.circular(9999), // ensures an oval
          ),
        ),

        // 2) Nine seat-circles around the oval
        ...List.generate(9, (seatIndex) {
          return _buildSeatCircle(
            seatIndex: seatIndex,
            tableWidth: tableWidth,
            tableHeight: tableHeight,
            hiveRecord: hiveRecord,
          );
        }),

        // 3) Board runout in the center
        Positioned(
          child: _buildBoardRunout(hiveRecord.boardCards),
        ),
      ],
    ),
  );
}

/// Creates one seat circle for the given seatIndex,
/// placing it around the oval based on angle.
/// Seat 0 now starts at the bottom center (offset by + π/2).
/// Creates one seat circle for the given seatIndex,
/// placing it around the oval based on angle.
/// Seat 0 now starts at the bottom center (offset by + π/2).
Widget _buildSeatCircle({
  required int seatIndex,
  required double tableWidth,
  required double tableHeight,
  required HandRecord hiveRecord,
}) {
  // Each seat is spaced by 2π/9 around the oval
  final double angleStep = (2 * pi) / 9;
  // Start seat 0 at bottom-center => offset angle by + π/2
  final double angle = seatIndex * angleStep + (pi / 2);

  // We'll push seats outward from the center of the oval a bit
  final double seatRadiusX = (tableWidth / 2) + 15;
  final double seatRadiusY = (tableHeight / 2) + 15;

  // We'll give each seat circle a fixed size
  const double seatSize = 50;

  // Determine if this seat is a winner
  final bool isWinner = hiveRecord.winnerSeats.contains(seatIndex);

  // Choose seat circle color
  final Color winColor = isWinner
      ? Colors.lightGreenAccent
      : Colors.blueGrey.shade700;

  return Positioned(
    left: (tableWidth / 2) + seatRadiusX * cos(angle) - (seatSize / 2) + 125,
    top:  (tableHeight / 2) + seatRadiusY * sin(angle) - (seatSize / 2) + 85,
    child: Container(
      width: seatSize,
      height: seatSize,
      decoration: BoxDecoration(
        shape: BoxShape.circle,
        color: Colors.blueGrey.shade700,
        border: Border.all(color: winColor, width: 1),
      ),
      child: _buildSeatContent(hiveRecord, seatIndex),
    ),
  );
}


/// In each seat, we show:
/// - “Seat X” label
/// - The seat’s hole cards (if not empty; otherwise no text).
Widget _buildSeatContent(HandRecord hiveRecord, int seatIndex) {
  final bool isActive = (seatIndex < hiveRecord.seatActive.length)
      ? hiveRecord.seatActive[seatIndex]
      : false;

  // Gather hole cards for this seat
  List<String> holeCards = <String>[];
  if (seatIndex < hiveRecord.holeCards.length) {
    holeCards = hiveRecord.holeCards[seatIndex];
  }

  // If this seat truly has no hole cards, don't show any text at all.
  if (holeCards.isEmpty) {
    return const SizedBox.shrink();
  }

  // Convert hole cards to e.g. "6♥ 6♠"
  final holeCardsStr = holeCards.map(_formatCard).join(" ");

  // If seat is not active, you can optionally gray it out
  final seatTextColor = isActive ? Colors.white : Colors.grey.shade400;

return SizedBox(
  width: 35,
  height: 35,
  child: Center(
    child: Column(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        Text(
          "Seat $seatIndex",
          style: TextStyle(fontSize: 10, color: seatTextColor),
        ),
        Text(
          holeCardsStr,
          style: TextStyle(fontSize: 10, color: seatTextColor),
        ),
      ],
    ),
  ),
);
}

/// Simply display the board cards as text in the center.
/// If no board, show “No Board”
Widget _buildBoardRunout(List<String> boardCards) {
  final String boardStr = boardCards.map(_formatCard).join(" ");
  return Text(
    boardStr.isEmpty ? "No Board" : boardStr,
    style: const TextStyle(color: Colors.white, fontSize: 14),
    textAlign: TextAlign.center,
  );
}

/// Helper to replace e.g. "6(heart)" with "6♥", etc.
String _formatCard(String raw) {
  return raw
      .replaceAll("(heart)", "♥")
      .replaceAll("(diamond)", "♦")
      .replaceAll("(club)", "♣")
      .replaceAll("(spade)", "♠");
}

/// The bottom portion with textual details:
/// (Now includes seatsSawFlop, seatsSawTurn, seatsSawRiver, plus “Copy” button).
/// The bottom portion with textual details:
/// (Now includes seatsSawFlop, seatsSawTurn, seatsSawRiver, plus “Copy” button).
Widget _buildHandRecordDetailBottom(HandRecord hiveRecord, BuildContext context) {
  return Column(
    crossAxisAlignment: CrossAxisAlignment.start,
    children: [
      Text(
        "Hand ID: ${hiveRecord.handId}",
        style: const TextStyle(
          color: Colors.white,
          fontSize: 12,
          fontWeight: FontWeight.bold,
        ),
      ),
      Text(
        "Timestamp: ${hiveRecord.timestamp}",
        style: const TextStyle(color: Colors.white, fontSize: 12),
      ),
      const SizedBox(height: 8),
      Text(
        "Table ID: ${hiveRecord.tableId}, maxPlayers: ${hiveRecord.maxPlayers}",
        style: const TextStyle(color: Colors.white, fontSize: 12),
      ),
      const SizedBox(height: 8),
      Text(
        "DealerIndex: ${hiveRecord.dealerIndex}, "
        "SB Index: ${hiveRecord.smallBlindIndex}, "
        "BB Index: ${hiveRecord.bigBlindIndex}",
        style: const TextStyle(color: Colors.white, fontSize: 12),
      ),
      Text(
        "Blinds: ${hiveRecord.smallBlind}/${hiveRecord.bigBlind}",
        style: const TextStyle(color: Colors.white, fontSize: 12),
      ),
      const SizedBox(height: 8),
      // Seats Saw Flop/Turn/River
      Text(
        "Seats Saw Flop: ${hiveRecord.seatsSawFlop}",
        style: const TextStyle(color: Colors.white, fontSize: 12),
      ),
      Text(
        "Seats Saw Turn: ${hiveRecord.seatsSawTurn}",
        style: const TextStyle(color: Colors.white, fontSize: 12),
      ),
      Text(
        "Seats Saw River: ${hiveRecord.seatsSawRiver}",
        style: const TextStyle(color: Colors.white, fontSize: 12),
      ),
      const SizedBox(height: 8),
      Text(
        "Players & Final Stacks:",
        style: const TextStyle(
          color: Colors.white,
          fontSize: 12,
          fontWeight: FontWeight.bold,
        ),
      ),
      for (int i = 0; i < hiveRecord.playerNames.length; i++)
        Text(
          "  Seat $i: ${hiveRecord.playerNames[i]}    "
          "Stack: ${hiveRecord.finalStacks[i]}    "
          "Active? ${hiveRecord.seatActive[i]}",
          style: const TextStyle(color: Colors.white, fontSize: 12),
        ),
      const SizedBox(height: 8),
      Text(
        "Hole Cards:",
        style: const TextStyle(
          color: Colors.white,
          fontSize: 12,
          fontWeight: FontWeight.bold,
        ),
      ),
      for (int i = 0; i < hiveRecord.holeCards.length; i++)
        Text(
          "  Seat $i => ${hiveRecord.holeCards[i].join(', ')}",
          style: const TextStyle(color: Colors.white, fontSize: 12),
        ),
      const SizedBox(height: 8),
      Text(
        "Burned Cards: ${hiveRecord.burnedCards.join(' ')}",
        style: const TextStyle(color: Colors.white, fontSize: 12),
      ),
      Text(
        "Board Cards: ${hiveRecord.boardCards.join(' ')}",
        style: const TextStyle(color: Colors.white, fontSize: 12),
      ),
      Text(
        "Main Pot: ${hiveRecord.mainPot}",
        style: const TextStyle(color: Colors.white, fontSize: 12),
      ),
      Text(
        "Side Pots: ${hiveRecord.sidePots}",
        style: const TextStyle(color: Colors.white, fontSize: 12),
      ),
      const SizedBox(height: 8),
      // WINNER SEATS
      Text(
        "Winner Seats: ${hiveRecord.winnerSeats}",
        style: const TextStyle(color: Colors.white, fontSize: 12),
      ),
      const SizedBox(height: 8),
      // Final Hand Descriptions, Showdown Cards
      Text(
        "Final Hand Descriptions: ${hiveRecord.finalHandDescriptions}",
        style: const TextStyle(color: Colors.white, fontSize: 12),
      ),
      Text(
        "Showdown Cards: ${hiveRecord.showdownCards}",
        style: const TextStyle(color: Colors.white, fontSize: 12),
      ),
      const SizedBox(height: 8),

      // Action History
      Text(
        "Action History:",
        style: const TextStyle(
          color: Colors.white,
          fontSize: 12,
          fontWeight: FontWeight.bold,
        ),
      ),
      Container(
        width: double.infinity,
        margin: const EdgeInsets.only(top: 4),
        decoration: BoxDecoration(
          color: Colors.blueGrey.shade700,
          borderRadius: BorderRadius.circular(4),
        ),
        padding: const EdgeInsets.all(8),
        child: SingleChildScrollView(
          child: Text(
            hiveRecord.actionHistory.join("\n"),
            style: const TextStyle(color: Colors.white, fontSize: 12),
          ),
        ),
      ),
      const SizedBox(height: 8),
      // Street Actions (expanded)
      Text(
        "Street Actions:",
        style: const TextStyle(
          color: Colors.white,
          fontSize: 12,
          fontWeight: FontWeight.bold,
        ),
      ),
      const SizedBox(height: 4),
      Container(
        width: double.infinity,
        margin: const EdgeInsets.only(bottom: 8),
        decoration: BoxDecoration(
          color: Colors.blueGrey.shade700,
          borderRadius: BorderRadius.circular(4),
        ),
        padding: const EdgeInsets.all(8),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: hiveRecord.streetActions.isEmpty
              ? [
                  const Text(
                    "No street actions recorded.",
                    style: TextStyle(color: Colors.white, fontSize: 12),
                  )
                ]
              : hiveRecord.streetActions.map((sa) {
                  return Text(
                    "Street: ${sa.streetName}  |  "
                    "Seat: ${sa.seatIndex}  |  "
                    "Action: ${sa.action}  |  "
                    "Amount: ${sa.amount}",
                    style: const TextStyle(color: Colors.white, fontSize: 12),
                  );
                }).toList(),
        ),
      ),
      const SizedBox(height: 8),

      // Stats Changes
      Container(
        width: double.infinity,
        margin: const EdgeInsets.only(bottom: 8),
        decoration: BoxDecoration(
          color: Colors.blueGrey.shade700,
          borderRadius: BorderRadius.circular(4),
        ),
        padding: const EdgeInsets.all(8),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: hiveRecord.statsChanges.isEmpty
              ? [
                  const Text(
                    "No stats changes recorded.",
                    style: TextStyle(color: Colors.white, fontSize: 12),
                  )
                ]
              : hiveRecord.statsChanges.entries.map((entry) {
                  final seatIndex = entry.key;
                  final changesList = entry.value; // e.g. ["foldTo3bet +1", "vpip +1"]
                  return Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        "Player ${seatIndex + 1}:",
                        style: const TextStyle(
                          color: Colors.lightGreenAccent,
                          fontSize: 12,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      for (final change in changesList)
                        Text(
                          "   $change",
                          style: const TextStyle(color: Colors.white, fontSize: 12),
                        ),
                    ],
                  );
                }).toList(),
        ),
      ),

      // NEW SECTION: Chip Changes
      const SizedBox(height: 8),
      Text(
        "Chip Changes:",
        style: const TextStyle(
          color: Colors.white,
          fontSize: 12,
          fontWeight: FontWeight.bold,
        ),
      ),
      const SizedBox(height: 4),
      // For each seat, display the difference between startingStacks[i] and finalStacks[i]
      for (int i = 0; i < hiveRecord.playerNames.length; i++)
        Builder(
          builder: (BuildContext _) {
            final startChips = hiveRecord.startingStacks[i];
            // Safely parse finalStacks[i], which is stored as a String
            final endChips = int.tryParse(hiveRecord.finalStacks[i]) ?? 0;
            final diff = endChips - startChips;

            // Determine color & text sign
            final diffColor = diff > 0
                ? Colors.greenAccent
                : diff < 0
                    ? Colors.redAccent
                    : Colors.grey;
            final sign = diff > 0 ? "+" : "";

            return Text(
              "  Seat $i (${hiveRecord.playerNames[i]}): $sign$diff",
              style: TextStyle(color: diffColor, fontSize: 12),
            );
          },
        ),

      // Row with COPY and CLOSE
      const SizedBox(height: 8),
      Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          ElevatedButton.icon(
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.blueGrey.shade600,
            ),
            icon: const Icon(Icons.copy, size: 16),
            label: const Text("Copy"),
            onPressed: () => _copyRecordDetails(hiveRecord, context),
          ),
          ElevatedButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text("Close"),
          ),
        ],
      ),
    ],
  );
}


/// Copies the entire record as text to the user’s clipboard, 
/// then shows a small SnackBar as confirmation.
void _copyRecordDetails(HandRecord hiveRecord, BuildContext context) {
  final sb = StringBuffer();
  sb.writeln("Hand ID: ${hiveRecord.handId}");
  sb.writeln("Timestamp: ${hiveRecord.timestamp}");
  sb.writeln("Table ID: ${hiveRecord.tableId}, maxPlayers: ${hiveRecord.maxPlayers}");
  sb.writeln("DealerIndex: ${hiveRecord.dealerIndex}, "
             "SB Index: ${hiveRecord.smallBlindIndex}, "
             "BB Index: ${hiveRecord.bigBlindIndex}");
  sb.writeln("Blinds: ${hiveRecord.smallBlind}/${hiveRecord.bigBlind}");
  sb.writeln("Seats Saw Flop: ${hiveRecord.seatsSawFlop}");
  sb.writeln("Seats Saw Turn: ${hiveRecord.seatsSawTurn}");
  sb.writeln("Seats Saw River: ${hiveRecord.seatsSawRiver}");

  sb.writeln("\nPlayers & Final Stacks:");
  for (int i = 0; i < hiveRecord.playerNames.length; i++) {
    sb.writeln("  Seat $i: ${hiveRecord.playerNames[i]}   "
               "Stack: ${hiveRecord.finalStacks[i]}   "
               "Active? ${hiveRecord.seatActive[i]}");
  }

  sb.writeln("\nHole Cards:");
  for (int i = 0; i < hiveRecord.holeCards.length; i++) {
    sb.writeln("  Seat $i => ${hiveRecord.holeCards[i].join(', ')}");
  }

  sb.writeln("Burned Cards: ${hiveRecord.burnedCards.join(' ')}");
  sb.writeln("Board Cards: ${hiveRecord.boardCards.join(' ')}");
  sb.writeln("Main Pot: ${hiveRecord.mainPot}");
  sb.writeln("Side Pots: ${hiveRecord.sidePots}");
  sb.writeln("Winner Seats: ${hiveRecord.winnerSeats}");
  sb.writeln("Final Hand Descriptions: ${hiveRecord.finalHandDescriptions}");
  sb.writeln("Showdown Cards: ${hiveRecord.showdownCards}");

  sb.writeln("\nAction History:");
  sb.writeln(hiveRecord.actionHistory.join("\n"));

  sb.writeln("\nStreet Actions:");
  if (hiveRecord.streetActions.isEmpty) {
    sb.writeln("  No street actions recorded.");
  } else {
    for (final sa in hiveRecord.streetActions) {
      sb.writeln("  Street: ${sa.streetName}, "
                 "Seat: ${sa.seatIndex}, "
                 "Action: ${sa.action}, "
                 "Amount: ${sa.amount}");
    }
  }

  Clipboard.setData(ClipboardData(text: sb.toString()));
  ScaffoldMessenger.of(context).showSnackBar(
    const SnackBar(
      content: Text("Copied mutation details to clipboard!"),
    ),
  );
}
