import 'package:flutter/material.dart';
import 'package:flutter/services.dart'; // for Clipboard

/// 3 states: closed, semiOpen, fullyOpen
enum HistoryPanelState {
  closed,
  semiOpen,
  fullyOpen,
}

/// A widget to display the Action/Hand History with a 3-step toggle:
/// 1) [HistoryPanelState.closed]: just a small tab (16px) with down arrow
/// 2) [HistoryPanelState.semiOpen]: shows last 5 entries on desktop, last 2 on mobile
/// 3) [HistoryPanelState.fullyOpen]: 220px, shows all lines + buttons
class ActionHistoryPanel extends StatelessWidget {
  final HistoryPanelState panelState;
  final VoidCallback onPanelToggle;
  final List<String> handHistory;
  final ScrollController historyScrollController;
  final VoidCallback onCopyHistory;
  final VoidCallback? onSwapCardView;
  final VoidCallback? onExitTable;

  static const Color _teal = Color.fromRGBO(3, 192, 193, 1);

  const ActionHistoryPanel({
    Key? key,
    required this.panelState,
    required this.onPanelToggle,
    required this.handHistory,
    required this.historyScrollController,
    required this.onCopyHistory,
    this.onSwapCardView,
    this.onExitTable,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final bool isMobile = MediaQuery.of(context).size.width < 600;

    // Teal overlay for interactions
    final overlay = MaterialStateProperty.resolveWith<Color?>((states) {
      if (states.contains(MaterialState.pressed) ||
          states.contains(MaterialState.hovered) ||
          states.contains(MaterialState.focused)) {
        return _teal.withOpacity(0.3);
      }
      return null;
    });

    // Determine container height
    double height;
    switch (panelState) {
      case HistoryPanelState.closed:
        height = 24;
        break;
      case HistoryPanelState.semiOpen:
        height = isMobile ? 65 : 115;
        break;
      case HistoryPanelState.fullyOpen:
        height = 220;
        break;
    }

    return MouseRegion(
      cursor: SystemMouseCursors.click,
      child: GestureDetector(
        onTap: onPanelToggle,
        child: AnimatedContainer(
          duration: const Duration(milliseconds: 200),
          width: 280,
          height: height,
          padding: const EdgeInsets.all(4),
          decoration: BoxDecoration(
            color: Colors.blueGrey.shade900,
            borderRadius: const BorderRadius.only(
              bottomRight: Radius.circular(8),
              bottomLeft: Radius.circular(8),
            ),
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              if (panelState == HistoryPanelState.fullyOpen)
                Padding(
                  padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.end,
                    children: [
                      ElevatedButton.icon(
                        onPressed: onCopyHistory,
                        icon: const Icon(Icons.copy, size: 16, color: _teal),
                        label: const Text(
                          "Copy History",
                          style: TextStyle(fontSize: 12, color: Colors.white70),
                        ),
                        style: ElevatedButton.styleFrom(
                          backgroundColor: Colors.blueGrey.shade900,
                          padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                        ).copyWith(overlayColor: overlay),
                      ),
                      if (isMobile && onSwapCardView != null) ...[
                        const SizedBox(width: 8),
                        ElevatedButton.icon(
                          onPressed: onSwapCardView,
                          icon: const Icon(Icons.swap_horiz, size: 16, color: _teal),
                          label: const Text(
                            "Swap Card View",
                            style: TextStyle(fontSize: 12, color: Colors.white70),
                          ),
                          style: ElevatedButton.styleFrom(
                            backgroundColor: Colors.blueGrey.shade900,
                            padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                          ).copyWith(overlayColor: overlay),
                        ),
                      ],
                      if (isMobile && onExitTable != null) ...[
                        const SizedBox(width: 8),
                        ElevatedButton.icon(
                          onPressed: onExitTable,
                          icon: const Icon(Icons.logout, size: 16, color: _teal),
                          label: const Text(
                            "Exit Table",
                            style: TextStyle(fontSize: 12, color: Colors.white70),
                          ),
                          style: ElevatedButton.styleFrom(
                            backgroundColor: Colors.blueGrey.shade900,
                            padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                          ).copyWith(overlayColor: overlay),
                        ),
                      ],
                    ],
                  ),
                ),

              Expanded(
                child: AnimatedSwitcher(
                  duration: const Duration(milliseconds: 200),
                  child: _buildContentForState(panelState, isMobile),
                ),
              ),

              GestureDetector(
                onTap: onPanelToggle,
                child: Container(
                  height: 16,
                  color: Colors.blueGrey.shade900,
                  alignment: Alignment.center,
                  child: Icon(
                    _arrowForState(panelState),
                    size: 16,
                    color: Colors.white70,
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  IconData _arrowForState(HistoryPanelState state) {
    switch (state) {
      case HistoryPanelState.closed:
      case HistoryPanelState.semiOpen:
        return Icons.keyboard_arrow_down;
      case HistoryPanelState.fullyOpen:
        return Icons.keyboard_arrow_up;
    }
  }

  Widget _buildContentForState(HistoryPanelState state, bool isMobile) {
    switch (state) {
      case HistoryPanelState.closed:
        return const SizedBox.shrink();
      case HistoryPanelState.semiOpen:
        return _buildSemiOpenHistory(isMobile);
      case HistoryPanelState.fullyOpen:
        return _buildFullyOpenHistory();
    }
  }

  Widget _buildSemiOpenHistory(bool isMobile) {
    final displayed = takeLast(handHistory, isMobile ? 2 : 5);
    return Stack(
      children: [
        Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: List.generate(displayed.length, (index) {
            final isLast = index == displayed.length - 1;
            final bgColor = (index % 2 == 0)
                ? Colors.blueGrey.shade800
                : Colors.blueGrey.shade600;
            return Container(
              decoration: BoxDecoration(
                color: bgColor,
                borderRadius: isLast
                    ? const BorderRadius.only(
                        bottomLeft: Radius.circular(6),
                        bottomRight: Radius.circular(6),
                      )
                    : null,
              ),
              padding: const EdgeInsets.symmetric(horizontal: 4, vertical: 0),
              child: Text(
                displayed[index],
                style: const TextStyle(color: Colors.white70, fontSize: 12),
                overflow: TextOverflow.ellipsis,
              ),
            );
          }),
        ),
        Positioned(
          top: 0,
          left: 0,
          right: 0,
          height: isMobile ? 5 : 50,
          child: Container(
            decoration: BoxDecoration(
              gradient: LinearGradient(
                begin: Alignment.topCenter,
                end: Alignment.bottomCenter,
                colors: [
                  Colors.blueGrey.shade900,
                  Colors.transparent,
                ],
              ),
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildFullyOpenHistory() {
    return Scrollbar(
      thumbVisibility: true,
      controller: historyScrollController,
      child: ListView.builder(
        controller: historyScrollController,
        itemCount: handHistory.length,
        itemBuilder: (context, index) {
          final bgColor = (index % 2 == 0)
              ? Colors.blueGrey.shade800
              : Colors.blueGrey.shade600;
          return Container(
            color: bgColor,
            padding: const EdgeInsets.symmetric(horizontal: 4, vertical: 0),
            child: Text(
              handHistory[index],
              style: const TextStyle(color: Colors.white70, fontSize: 12),
            ),
          );
        },
      ),
    );
  }

  List<String> takeLast(List<String> list, int n) {
    if (n <= 0) return [];
    if (n >= list.length) return list;
    return list.sublist(list.length - n);
  }
}
