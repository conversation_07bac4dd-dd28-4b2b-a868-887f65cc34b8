import 'package:flutter/material.dart';

class HeaderComponent extends StatelessWidget {
  final VoidCallback? onMenuTap;
  final bool actionLogOpened;
  HeaderComponent({Key? key, this.onMenuTap, required this.actionLogOpened}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.fromLTRB(24, 0, 24, 0),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Row(
            children: [
              Image.network(
                'assets/images/tinycards.png',
                width: 32,
                height: 32,
                fit: BoxFit.contain,
              ),
              const SizedBox(width: 8),
              Text(
                'ACTION LOG',
                style: headerTextStyle(),
              ),
            ],
          ),
          Container(
            alignment: Alignment.center,
            width: 44,
            height: 44,
              decoration: BoxDecoration(
                gradient: const LinearGradient(
                  colors: [
                    Color.fromARGB(255, 0, 88, 91),
                    Color.fromARGB(255, 2, 34, 37)
                  ],
                  begin: Alignment.topCenter,
                  end: Alignment.bottomCenter,
                ),
                border: Border.all(color: Colors.black, width:  1),
                // color: const Color(0xFF06606B),
                borderRadius: BorderRadius.circular(12),
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withOpacity(0.5),
                    blurRadius: 32,
                    offset: const Offset(0, 8),
                  ),
                ],
              ),
            child: InkWell(
              onTap: onMenuTap,
              borderRadius: BorderRadius.circular(12),
              child: Center(
                child: Icon(
                  actionLogOpened ? Icons.add : Icons.remove,
                  color: Colors.white,
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  TextStyle headerTextStyle() {
    return const TextStyle(
      fontFamily: 'Inter',
      fontWeight: FontWeight.w900,
      fontSize: 16,
      color: Colors.white,
      shadows: [
        Shadow(
          color: Color(0xE0000000),
          blurRadius: 2.5,
        ),
      ],
    );
  }
}

